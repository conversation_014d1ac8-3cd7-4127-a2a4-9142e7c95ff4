CONTRIBUTING.md
LICENSE
MANIFEST.in
README.md
demo.py
setup.py
tutorial.ipynb
itas/__init__.py
itas/eval_utils.py
itas/group_prompts.py
itas/kc_probing.py
itas/mutual_information_and_expectation.py
itas/patch_utils.py
itas/prepare_eval.py
itas/sae_repe_utils.py
itas/save_grouped_activations.py
itas/spare_for_generation.py
itas/utils.py
itas.egg-info/PKG-INFO
itas.egg-info/SOURCES.txt
itas.egg-info/dependency_links.txt
itas.egg-info/entry_points.txt
itas.egg-info/requires.txt
itas.egg-info/top_level.txt
itas/analysis/__init__.py
itas/analysis/activation_analyzer.py
itas/analysis/activation_patterns.py
itas/analysis/analysis_save_activations.py
itas/analysis/evaluation.py
itas/analysis/function_extractor.py
itas/analysis/group_instance.py
itas/analysis/representation_engineer.py
itas/analysis/visualization.py
itas/core/__init__.py
itas/core/activations_store.py
itas/core/config.py
itas/core/dataset_manager.py
itas/core/model_loader.py
itas/core/sae.py
itas/core/trainer.py
itas/datasets/__init__.py
itas/datasets/eval_datasets_macnoise.py
itas/datasets/eval_datasets_nqswap.py
itas/datasets/function_extraction_datasets.py
itas/function_extraction_modellings/__init__.py
itas/function_extraction_modellings/function_extraction_gemma2.py
itas/function_extraction_modellings/function_extraction_llama.py
itas/function_extraction_modellings/function_extractor.py
itas/sae/__init__.py
itas/sae/__main__.py
itas/sae/config.py
itas/sae/data.py
itas/sae/kernels.py
itas/sae/sae.py
itas/sae/trainer.py
itas/sae/utils.py
itas/sae_lens/__init__.py
itas/sae_lens/cache_activations_runner.py
itas/sae_lens/config.py
itas/sae_lens/eleuther_sae_wrapper.py
itas/sae_lens/evals.py
itas/sae_lens/load_model.py
itas/sae_lens/pretokenize_runner.py
itas/sae_lens/pretrained_saes.yaml
itas/sae_lens/sae.py
itas/sae_lens/sae_training_runner.py
itas/sae_lens/tokenization_and_batching.py
itas/sae_lens/toy_model_runner.py
itas/sae_lens/analysis/__init__.py
itas/sae_lens/analysis/feature_statistics.py
itas/sae_lens/analysis/hooked_sae_transformer.py
itas/sae_lens/analysis/neuronpedia_integration.py
itas/sae_lens/analysis/tsea.py
itas/sae_lens/toolkit/__init__.py
itas/sae_lens/toolkit/pretrained_sae_loaders.py
itas/sae_lens/toolkit/pretrained_saes.py
itas/sae_lens/toolkit/pretrained_saes_directory.py
itas/sae_lens/training/__init__.py
itas/sae_lens/training/activations_store.py
itas/sae_lens/training/geometric_median.py
itas/sae_lens/training/optim.py
itas/sae_lens/training/sae_trainer.py
itas/sae_lens/training/toy_models.py
itas/sae_lens/training/train_toy_sae.py
itas/sae_lens/training/training_sae.py
itas/sae_lens/training/upload_saes_to_huggingface.py