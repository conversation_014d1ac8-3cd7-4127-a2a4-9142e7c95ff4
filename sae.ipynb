{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/mnt/raid6/junkim100/miniconda3/envs/spare/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from spare.sae import Sae\n", "\n", "def load_sae(model_name, layer_idx):\n", "    if model_name == \"Meta-Llama-3-1-8B\":\n", "        sae = Sae.load_from_hub(\n", "            \"EleutherAI/sae-llama-3.1-8b-32x\", hookpoint=f\"layers.{layer_idx}.mlp\"\n", "        )\n", "    else:\n", "        raise NotImplementedError(f\"sae for {model_name}\")\n", "\n", "    for pn, p in sae.named_parameters():\n", "        p.requires_grad = False\n", "    sae.cuda()\n", "    return sae"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_sae(\"Meta-Llama-3-1-8B\", 12)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Fetching 3 files: 100%|██████████| 3/3 [01:09<00:00, 23.23s/it]\n", "Dropping extra args {'signed': False}\n"]}], "source": ["sae = Sae.load_from_hub(\"EleutherAI/sae-llama-3.1-8b-32x\", hookpoint=\"layers.23.mlp\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "spare", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 2}