{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ITAS Tutorial: End-to-End SAE Training with LLaMA 3.1 8B Instruct\n", "\n", "This comprehensive tutorial demonstrates how to use ITAS (Instruction-Truth Activation Steering) to train and analyze sparse autoencoders on any HuggingFace model. We'll use LLaMA 3.1 8B Instruct as our example model.\n", "\n", "## 🎯 What You'll Learn\n", "\n", "1. **Model Setup**: Load and configure LLaMA 3.1 8B Instruct\n", "2. **Dataset Preparation**: Process WikiText dataset for SAE training\n", "3. **SAE Training**: Train different SAE architectures (Standard, Gated, JumpReLU)\n", "4. **Function Extraction**: Extract behavioral functions from trained SAEs\n", "5. **Representation Engineering**: Create steering vectors for behavior modification\n", "6. **Evaluation**: Comprehensive SAE quality assessment\n", "7. **Visualization**: Create insightful plots and analysis\n", "\n", "## 📋 Prerequisites\n", "\n", "- Python 3.8+\n", "- CUDA-capable GPU (recommended: 24GB+ VRAM for LLaMA 3.1 8B)\n", "- HuggingFace account with access to LLaMA models\n", "\n", "## 🚀 Let's Get Started!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Environment Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install ITAS if not already installed\n", "# !pip install itas\n", "\n", "import os\n", "import torch\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from transformers import AutoTokenizer\n", "import logging\n", "\n", "# ITAS imports\n", "import itas\n", "from itas import (\n", "    SAEConfig, ModelConfig, DatasetConfig, TrainingConfig,\n", "    UniversalModel<PERSON><PERSON>der, DatasetManager, validate_config\n", ")\n", "from itas.core.sae import TrainingSAE\n", "from itas.core.activations_store import ActivationsStore\n", "from torch.nn import DataParallel\n", "\n", "# Set up logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "def get_available_gpus():\n", "    \"\"\"Get list of available GPU device IDs.\"\"\"\n", "    if not torch.cuda.is_available():\n", "        return []\n", "\n", "    # Get CUDA_VISIBLE_DEVICES if set\n", "    visible_devices = os.environ.get(\"CUDA_VISIBLE_DEVICES\")\n", "    if visible_devices:\n", "        # Parse comma-separated device IDs\n", "        try:\n", "            device_ids = [int(d.strip()) for d in visible_devices.split(\",\") if d.strip()]\n", "            # Map to 0-indexed for torch\n", "            return list(range(len(device_ids)))\n", "        except ValueError:\n", "            # Fallback if parsing fails\n", "            return list(range(torch.cuda.device_count()))\n", "    else:\n", "        return list(range(torch.cuda.device_count()))\n", "\n", "def detect_gpu_setup():\n", "    \"\"\"Detect and configure GPU setup.\"\"\"\n", "    # Set CUDA devices if not already set\n", "    if \"CUDA_VISIBLE_DEVICES\" not in os.environ:\n", "        os.environ[\"CUDA_DEVICE_ORDER\"] = \"PCI_BUS_ID\"\n", "        os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0,1,2,3\"  # Default to first 4 GPUs\n", "\n", "    # Check GPU availability\n", "    device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "    num_gpus = torch.cuda.device_count()\n", "\n", "    print(f\"🔧 GPU Setup:\")\n", "    print(f\"  Using device: {device}\")\n", "    print(f\"  Available GPUs: {num_gpus}\")\n", "    print(f\"  CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES', 'Not set')}\")\n", "\n", "    if device == \"cuda\":\n", "        for i in range(num_gpus):\n", "            print(f\"  GPU {i}: {torch.cuda.get_device_name(i)}\")\n", "            print(f\"    VRAM: {torch.cuda.get_device_properties(i).total_memory / 1e9:.1f} GB\")\n", "\n", "    return device, num_gpus\n", "\n", "# Detect GPU setup\n", "device, num_gpus = detect_gpu_setup()\n", "gpu_ids = get_available_gpus()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Model Setup and Configuration\n", "\n", "First, let's load LLaMA 3.1 8B Instruct and explore its architecture."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Model configuration\n", "model_name = \"meta-llama/Llama-3.1-8B-Instruct\"\n", "\n", "# Note: You need HuggingFace access to LLaMA models\n", "# Make sure you're logged in: huggingface-cli login\n", "\n", "print(f\"Loading {model_name}...\")\n", "print(f\"Using {'multi-GPU' if num_gpus > 1 else 'single-GPU'} setup with {num_gpus} GPU(s)\")\n", "\n", "# Use balanced device mapping for better memory distribution\n", "device_map = \"balanced\" if num_gpus > 1 else \"auto\"\n", "\n", "# Load model and tokenizer with optimizations\n", "model_loader = UniversalModelLoader(\n", "    ModelConfig(\n", "        model_name=model_name,\n", "        use_flash_attention=True,  # Automatic compatibility detection\n", "        torch_dtype=\"bfloat16\",    # Memory efficient\n", "        device_map=device_map,     # Optimized device placement\n", "        trust_remote_code=False\n", "    )\n", ")\n", "\n", "model, tokenizer = model_loader.load_model_and_tokenizer()\n", "\n", "print(f\"✓ Model loaded successfully!\")\n", "print(f\"Model device mapping: {getattr(model, 'hf_device_map', 'Single device')}\")\n", "print(f\"Model dtype: {next(model.parameters()).dtype}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Explore Model Architecture"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get detailed model information\n", "model_info = model_loader.get_model_info()\n", "hook_names = model_loader.get_hook_names()\n", "\n", "print(\"📊 Model Information:\")\n", "print(f\"  Model: {model_info['model_name']}\")\n", "print(f\"  Architecture: {model_info['architecture']}\")\n", "print(f\"  Hidden size: {model_info['hidden_size']}\")\n", "print(f\"  Number of layers: {model_info['num_layers']}\")\n", "print(f\"  Total parameters: {model_info['total_parameters']:,}\")\n", "print(f\"  Vocabulary size: {model_info['vocab_size']:,}\")\n", "\n", "print(\"\\n🔗 Available Hook Points:\")\n", "for hook_type, hooks in list(hook_names.items()):\n", "    print(f\"  {hook_type}: {len(hooks)} hooks\")\n", "    if hooks:\n", "        print(f\"    Example: {hooks}\")\n", "\n", "# Choose a middle layer for SAE training (good balance of complexity and interpretability)\n", "target_layer = model_info['num_layers'] // 2\n", "print(f\"\\n🎯 Target layer for SAE training: {target_layer}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Dataset Preparation\n", "\n", "Let's prepare the WikiText dataset for SAE training."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Dataset configuration\n", "dataset_config = DatasetConfig(\n", "    dataset_name=\"wikitext\",\n", "    dataset_kwargs={\"name\": \"wikitext-2-raw-v1\"},  # Specify WikiText variant\n", "    dataset_split=\"train\",\n", "    text_column=\"text\",\n", "    max_seq_length=2048,  # LLaMA 3.1 context length\n", "    chunk_size=2048,\n", "    streaming=False,  # Load full dataset for tutorial\n", "    num_proc=4,  # Parallel processing\n", "    trust_remote_code=False,\n", ")\n", "\n", "print(\"📚 Loading and preprocessing dataset...\")\n", "\n", "# Initialize dataset manager\n", "dataset_manager = DatasetManager(dataset_config, tokenizer)\n", "\n", "# Load and preprocess dataset\n", "dataset = dataset_manager.load_dataset()\n", "processed_dataset = dataset_manager.preprocess_dataset()\n", "\n", "# Get dataset statistics\n", "dataset_info = dataset_manager.get_dataset_info()\n", "print(f\"✓ Dataset loaded successfully!\")\n", "print(f\" Dataset: {dataset_info['dataset_name']}\")\n", "print(f\"  Raw size: {dataset_info['raw_size']:,} examples\")\n", "print(f\"  Processed size: {dataset_info['processed_size']:,} examples\")\n", "\n", "total_tokens_val = dataset_info.get('total_tokens', 'Unknown')\n", "if isinstance(total_tokens_val, (int, float)):\n", "    print(f\"  Total tokens: {total_tokens_val:,}\")\n", "else:\n", "    print(f\"  Total tokens: {total_tokens_val}\")\n", "\n", "# Show a sample\n", "sample = processed_dataset[0]\n", "print(f\"\\n📝 Sample text (first 200 chars):\")\n", "\n", "input_ids_value = sample.get('input_ids')\n", "\n", "if input_ids_value is None:\n", "    print(\"'(<PERSON><PERSON> is missing input_ids)'\")\n", "# Check if it's a tensor-like object with an 'ndim' attribute (like PyTorch/TensorFlow tensors)\n", "elif hasattr(input_ids_value, 'ndim') and input_ids_value.ndim == 0:\n", "    print(f\"Debug: input_ids is a 0-dim tensor. Value: {input_ids_value.item() if hasattr(input_ids_value, 'item') else input_ids_value}\")\n", "    # To decode a single token, it often needs to be in a sequence (e.g., a list or 1D tensor)\n", "    # For PyTorch tensor:\n", "    if hasattr(input_ids_value, 'unsqueeze'):\n", "        tokens_to_decode = input_ids_value.unsqueeze(0) # Convert tensor(X) to tensor([X])\n", "        print(f\"'{tokenizer.decode(tokens_to_decode)}...' (Note: input_ids was a 0-dim tensor)\")\n", "    else: # Fallback if not a PyTorch tensor but still 0-dim somehow\n", "        print(f\"'{tokenizer.decode([input_ids_value])}...' (Note: input_ids was a 0-dim value, attempting decode)\")\n", "elif hasattr(input_ids_value, '__len__') and len(input_ids_value) > 0: # List or 1D tensor with elements\n", "    tokens_to_show = input_ids_value[:50]\n", "    print(f\"'{tokenizer.decode(tokens_to_show)}...'\")\n", "else: # Empty list, empty tensor, or other unexpected type\n", "    print(\"'(Sample input_ids is empty or not in a decodable format)'\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: SAE Configuration and Training\n", "\n", "Now let's configure and train different SAE architectures."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.1 Create SAE Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get the correct hook names for this model\n", "hook_names = model_loader.get_hook_names()\n", "mlp_hook_pattern = hook_names['mlp_out']\n", "mlp_hook_name = mlp_hook_pattern.format(layer=target_layer)\n", "\n", "print(f\"🔗 Available hook patterns: {list(hook_names.keys())}\")\n", "print(f\"🎯 Using MLP hook: {mlp_hook_name}\")\n", "\n", "# Create comprehensive SAE configuration\n", "config = SAEConfig(\n", "    # Model configuration\n", "    model=ModelConfig(\n", "        model_name=model_name,\n", "        use_flash_attention=True,\n", "        torch_dtype=\"bfloat16\",\n", "        trust_remote_code=False,\n", "        device_map=\"auto\",\n", "    ),\n", "\n", "    # Dataset configuration\n", "    dataset=dataset_config,\n", "\n", "    # Training configuration\n", "    training=TrainingConfig(\n", "        total_training_tokens=50_000_000,  # 50M tokens for tutorial\n", "        batch_size=4096 * max(1, num_gpus // 2),  # Scaled for multi-GPU\n", "        learning_rate=3e-4,\n", "        l1_coefficient=1e-3,               # Sparsity regularization\n", "        lr_scheduler=\"cosine\",\n", "        lr_warm_up_steps=1000,\n", "\n", "        # Checkpointing and logging\n", "        checkpoint_every_n_tokens=10_000_000,\n", "        save_checkpoint_dir=\"./checkpoints\",\n", "        log_every_n_steps=100,\n", "        eval_every_n_tokens=5_000_000,\n", "\n", "        # No W&B for tutorial\n", "        use_wandb=False,\n", "    ),\n", "\n", "    # SAE architecture\n", "    architecture=\"gated\",              # Start with gated SAE (best performance)\n", "    expansion_factor=32,               # 32x expansion (4096 -> 131,072 features)\n", "    hook_layer=target_layer,           # Middle layer\n", "    hook_name=mlp_hook_name,           # Use correct hook name for this model\n", "    activation_fn=\"relu\",\n", "    normalize_decoder=True,\n", "\n", "    # Device and precision\n", "    device=\"cuda:0\",                   # Primary device\n", "    dtype=\"float32\",                   # Training precision\n", "    seed=42,\n", ")\n", "\n", "print(\"\\n⚙️ SAE Configuration:\")\n", "print(f\"  Architecture: {config.architecture}\")\n", "print(f\"  Hook layer: {config.hook_layer}\")\n", "print(f\"  Hook name: {config.hook_name}\")\n", "print(f\"  Expansion factor: {config.expansion_factor}\")\n", "print(f\"  Hidden size: {model_info['hidden_size']}\")\n", "print(f\"  SAE features: {model_info['hidden_size'] * config.expansion_factor:,}\")\n", "print(f\"  Training tokens: {config.training.total_training_tokens:,}\")\n", "print(f\"  Batch size: {config.training.batch_size}\")\n", "\n", "# Validate configuration\n", "try:\n", "    issues = validate_config(config)\n", "    if issues:\n", "        print(f\"❌ Configuration issues found: {issues}\")\n", "        raise ValueError(f\"Invalid configuration: {issues}\")\n", "    else:\n", "        print(\"✓ Configuration is valid!\")\n", "except Exception as e:\n", "    print(f\"❌ Configuration error: {e}\")\n", "    raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Train the SAE\n", "\n", "Now let's train our first SAE! This will take some time depending on your hardware."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class SAETrainer:\n", "    \"\"\"Scalable SAE trainer that works with single or multiple GPUs using DataParallel.\"\"\"\n", "\n", "    def __init__(self, config, gpu_ids=None):\n", "        self.config = config\n", "        self.gpu_ids = gpu_ids or [0]\n", "        self.num_gpus = len(self.gpu_ids)\n", "        self.device = f\"cuda:{self.gpu_ids[0]}\" if torch.cuda.is_available() else \"cpu\"\n", "\n", "        # Initialize components\n", "        self.model = None\n", "        self.tokenizer = None\n", "        self.sae = None\n", "        self.optimizer = None\n", "        self.dataset_manager = None\n", "\n", "    def setup(self):\n", "        \"\"\"Setup model, SAE, and data components.\"\"\"\n", "        print(\n", "            f\"🔧 Setting up {'multi-GPU' if self.num_gpus > 1 else 'single-GPU'} SAE training...\"\n", "        )\n", "        print(f\"  Using GPUs: {self.gpu_ids}\")\n", "\n", "        # Load model and tokenizer\n", "        model_loader = UniversalModelLoader(self.config.model)\n", "        self.model, self.tokenizer = model_loader.load_model_and_tokenizer()\n", "\n", "        # Get model info and update config\n", "        model_info = model_loader.get_model_info()\n", "        hook_names = model_loader.get_hook_names()\n", "\n", "        # Update target layer and hook name\n", "        target_layer = model_info[\"num_layers\"] // 2\n", "        self.config.hook_layer = target_layer\n", "\n", "        if \"mlp_out\" in hook_names:\n", "            mlp_hook_pattern = hook_names[\"mlp_out\"]\n", "            self.config.hook_name = mlp_hook_pattern.format(layer=target_layer)\n", "\n", "        print(f\"✓ Model loaded: {model_info['model_name']}\")\n", "        print(f\"  Architecture: {model_info['architecture']}\")\n", "        print(f\"  Hidden size: {model_info['hidden_size']}\")\n", "        print(f\"  Layers: {model_info['num_layers']}\")\n", "        print(f\"  Target layer: {target_layer}\")\n", "        print(f\"  Hook name: {self.config.hook_name}\")\n", "\n", "        # Setup dataset\n", "        self.dataset_manager = DatasetManager(self.config.dataset, self.tokenizer)\n", "        self.dataset_manager.load_dataset()\n", "        self.dataset_manager.preprocess_dataset()\n", "\n", "        dataset_info = self.dataset_manager.get_dataset_info()\n", "        print(f\"✓ Dataset loaded: {dataset_info['processed_size']:,} samples\")\n", "\n", "        # Initialize SAE\n", "        d_in = model_info[\"hidden_size\"]\n", "\n", "        self.sae = TrainingSAE(\n", "            d_in=d_in,\n", "            d_sae=d_in * self.config.expansion_factor,\n", "            architecture=self.config.architecture,\n", "            activation_fn=self.config.activation_fn,\n", "            normalize_decoder=self.config.normalize_decoder,\n", "            device=self.device,\n", "            dtype=getattr(torch, self.config.dtype),\n", "        )\n", "\n", "        # Wrap SAE with DataParallel if multi-GPU\n", "        if self.num_gpus > 1:\n", "            print(f\"🔥 Enabling DataParallel across {self.num_gpus} GPUs\")\n", "            self.sae = DataParallel(self.sae, device_ids=self.gpu_ids)\n", "\n", "        # Initialize optimizer\n", "        self.optimizer = torch.optim.Adam(\n", "            self.sae.parameters(),\n", "            lr=self.config.training.learning_rate,\n", "            betas=(0.9, 0.999),\n", "            weight_decay=0.0,\n", "        )\n", "\n", "        print(f\"✓ SAE initialized\")\n", "        print(f\"  d_in: {d_in}\")\n", "        print(f\"  d_sae: {d_in * self.config.expansion_factor}\")\n", "        print(f\"  Architecture: {self.config.architecture}\")\n", "        print(f\"  Multi-GPU: {self.num_gpus > 1}\")\n", "        print(f\"  Effective batch size: {self.config.training.batch_size}\")\n", "\n", "        # Initialize W&B if enabled\n", "        if self.config.training.use_wandb:\n", "            wandb.init(\n", "                project=self.config.training.wandb_project,\n", "                name=self.config.training.wandb_run_name,\n", "                config={\n", "                    \"model_name\": self.config.model.model_name,\n", "                    \"dataset_name\": self.config.dataset.dataset_name,\n", "                    \"architecture\": self.config.architecture,\n", "                    \"expansion_factor\": self.config.expansion_factor,\n", "                    \"hook_layer\": self.config.hook_layer,\n", "                    \"hook_name\": self.config.hook_name,\n", "                    \"d_in\": d_in,\n", "                    \"d_sae\": d_in * self.config.expansion_factor,\n", "                    \"total_training_tokens\": self.config.training.total_training_tokens,\n", "                    \"batch_size\": self.config.training.batch_size,\n", "                    \"learning_rate\": self.config.training.learning_rate,\n", "                    \"l1_coefficient\": self.config.training.l1_coefficient,\n", "                    \"num_gpus\": self.num_gpus,\n", "                    \"gpu_ids\": self.gpu_ids,\n", "                },\n", "            )\n", "            print(\n", "                f\"✓ W&B initialized: {self.config.training.wandb_project}/{self.config.training.wandb_run_name}\"\n", "            )\n", "\n", "    def train(self):\n", "        \"\"\"Train the SAE.\"\"\"\n", "        print(\"🏋️ Starting SAE training...\")\n", "\n", "        # Setup activations store\n", "        activations_store = ActivationsStore(\n", "            self.model,\n", "            self.tokenizer,\n", "            self.config,\n", "            self.dataset_manager,\n", "        )\n", "\n", "        # Training parameters\n", "        total_tokens = self.config.training.total_training_tokens\n", "        batch_size = self.config.training.batch_size\n", "        total_steps = total_tokens // batch_size\n", "\n", "        print(f\"  Total tokens: {total_tokens:,}\")\n", "        print(f\"  Batch size: {batch_size}\")\n", "        print(f\"  Total steps: {total_steps:,}\")\n", "        print(f\"  GPUs: {self.num_gpus}\")\n", "\n", "        # Estimate training time\n", "        tokens_per_second_estimate = 1000 * self.num_gpus  # Scale with GPUs\n", "        estimated_hours = total_tokens / tokens_per_second_estimate / 3600\n", "        print(f\"  Estimated time: {estimated_hours:.1f} hours\")\n", "\n", "        # Training loop\n", "        step = 0\n", "        tokens_processed = 0\n", "\n", "        try:\n", "            with activations_store:\n", "                activations_store.start_streaming(batch_size=32)\n", "\n", "                while tokens_processed < total_tokens and step < total_steps:\n", "                    # Get batch of activations\n", "                    activations = activations_store.get_next_batch(timeout=10.0)\n", "                    if activations is None:\n", "                        continue\n", "\n", "                    # Move to device and prepare\n", "                    activations = activations.to(self.device).float()\n", "                    if len(activations.shape) > 2:\n", "                        activations = activations.view(-1, activations.shape[-1])\n", "\n", "                    # Limit batch size\n", "                    if activations.shape[0] > batch_size:\n", "                        activations = activations[:batch_size]\n", "\n", "                    # Training step\n", "                    self.optimizer.zero_grad()\n", "\n", "                    # Forward pass - DataParallel handles multi-GPU automatically\n", "                    if self.num_gpus > 1:\n", "                        # For DataParallel, we need to use the standard forward method\n", "                        output = self.sae(activations)\n", "                        # Manually compute loss for DataParallel\n", "                        l1_coeff = self.config.training.l1_coefficient\n", "                        total_loss = output.mse_loss + l1_coeff * output.l1_loss\n", "                    else:\n", "                        # Single GPU can use training_forward\n", "                        output = self.sae.training_forward(\n", "                            activations,\n", "                            l1_coefficient=self.config.training.l1_coefficient,\n", "                        )\n", "                        total_loss = output.mse_loss + output.l1_loss\n", "                        if output.aux_loss is not None:\n", "                            total_loss = total_loss + output.aux_loss\n", "\n", "                    # Backward pass\n", "                    total_loss.backward()\n", "\n", "                    # Gradient clipping\n", "                    torch.nn.utils.clip_grad_norm_(self.sae.parameters(), max_norm=1.0)\n", "\n", "                    # Optimizer step\n", "                    self.optimizer.step()\n", "\n", "                    # Update counters\n", "                    step += 1\n", "                    tokens_processed += activations.shape[0]\n", "\n", "                    # Logging\n", "                    if step % self.config.training.log_every_n_steps == 0:\n", "                        metrics = {\n", "                            \"step\": step,\n", "                            \"tokens_processed\": tokens_processed,\n", "                            \"total_loss\": total_loss.item(),\n", "                            \"mse_loss\": output.mse_loss.item(),\n", "                            \"l1_loss\": output.l1_loss.item(),\n", "                            \"progress\": tokens_processed / total_tokens,\n", "                        }\n", "\n", "                        # Add auxiliary loss if available\n", "                        if hasattr(output, \"aux_loss\") and output.aux_loss is not None:\n", "                            metrics[\"aux_loss\"] = output.aux_loss.item()\n", "\n", "                        # Console logging\n", "                        print(\n", "                            f\"Step {step:,}/{total_steps:,} | \"\n", "                            f\"Tokens: {tokens_processed:,}/{total_tokens:,} | \"\n", "                            f\"Loss: {total_loss.item():.6f} | \"\n", "                            f\"MSE: {output.mse_loss.item():.6f} | \"\n", "                            f\"L1: {output.l1_loss.item():.6f}\"\n", "                        )\n", "\n", "                        # W&B logging\n", "                        if self.config.training.use_wandb:\n", "                            wandb.log(metrics)\n", "\n", "                activations_store.stop_streaming()\n", "\n", "        except Exception as e:\n", "            logger.error(f\"Training failed: {e}\")\n", "            raise\n", "\n", "        print(\"✅ Training completed!\")\n", "\n", "        # Finish W&B run\n", "        if self.config.training.use_wandb:\n", "            wandb.finish()\n", "            print(\"✓ W&B run finished\")\n", "\n", "        # Return the underlying module if wrapped with DataParallel\n", "        return self.sae.module if self.num_gpus > 1 else self.sae"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train the SAE\n", "print(\"🏋️ Starting SAE training...\")\n", "print(f\"Training configuration:\")\n", "print(f\"  Total tokens: {config.training.total_training_tokens:,}\")\n", "print(f\"  Batch size: {config.training.batch_size}\")\n", "print(f\"  Learning rate: {config.training.learning_rate}\")\n", "print(f\"  L1 coefficient: {config.training.l1_coefficient}\")\n", "print(f\"  Architecture: {config.architecture}\")\n", "print(f\"  Hook: {config.hook_name}\")\n", "print(\"\\nThis may take 30-60 minutes depending on your hardware...\\n\")\n", "\n", "# Initialize trainer\n", "trainer = <PERSON><PERSON><PERSON><PERSON>(config)\n", "\n", "# Train the SAE\n", "sae = trainer.train()\n", "\n", "# Save the trained SAE\n", "save_path = f\"llama_3_1_8b_layer{target_layer}_gated_sae.pt\"\n", "trainer.save_model(save_path)\n", "\n", "print(f\"\\n✅ Training completed successfully!\")\n", "print(f\"📁 SAE saved to: {save_path}\")\n", "\n", "# Get training metrics\n", "training_metrics = trainer.get_training_metrics()\n", "print(f\"\\n📊 Training Summary:\")\n", "print(f\"  Total steps: {training_metrics['total_steps']}\")\n", "print(f\"  Total tokens: {training_metrics['total_tokens']:,}\")\n", "print(f\"  Final loss: {training_metrics['final_loss']:.6f}\")\n", "print(f\"  Final MSE: {training_metrics['final_mse_loss']:.6f}\")\n", "print(f\"  Final L1: {training_metrics['final_l1_loss']:.6f}\")\n", "print(f\"  Final sparsity: {training_metrics['final_sparsity']:.4f}\")\n", "print(f\"  Final FVU: {training_metrics['final_fvu']:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 Quick SAE Evaluation\n", "\n", "Let's quickly evaluate our trained SAE to see how well it performed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load sae from disk\n", "sae ="]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Quick evaluation of the trained SAE\n", "print(\"📊 Evaluating trained SAE...\")\n", "\n", "# Get some test activations\n", "test_texts = [\n", "    \"The capital of France is Paris.\",\n", "    \"Machine learning is a subset of artificial intelligence.\",\n", "    \"The mitochondria is the powerhouse of the cell.\",\n", "    \"<PERSON> wrote <PERSON> and Juliet.\",\n", "    \"Python is a popular programming language.\"\n", "]\n", "\n", "# Tokenize test texts\n", "test_inputs = tokenizer(test_texts, return_tensors=\"pt\", padding=True, truncation=True)\n", "test_inputs = {k: v.to(device) for k, v in test_inputs.items()}\n", "\n", "# Get activations from the model\n", "with torch.no_grad():\n", "    outputs = model(**test_inputs, output_hidden_states=True)\n", "    # Extract activations from our target layer\n", "    test_activations = outputs.hidden_states[target_layer]  # Shape: [batch, seq_len, hidden_size]\n", "    # Flatten to [batch * seq_len, hidden_size]\n", "    test_activations = test_activations.view(-1, test_activations.size(-1))\n", "\n", "print(f\"Test activations shape: {test_activations.shape}\")\n", "\n", "# Test SAE reconstruction\n", "with torch.no_grad():\n", "    # Convert to float32 for SAE processing\n", "    test_activations_f32 = test_activations.float()\n", "    sae_output = sae(test_activations_f32)\n", "\n", "    # Extract outputs using correct attribute names\n", "    reconstructed = sae_output.sae_out  # Reconstructed activations\n", "    feature_activations = sae_output.feature_acts  # Feature activations\n", "\n", "# Calculate basic metrics\n", "mse = torch.mean((test_activations_f32 - reconstructed) ** 2).item()\n", "cosine_sim = torch.nn.functional.cosine_similarity(\n", "    test_activations_f32.flatten(), reconstructed.flatten(), dim=0\n", ").item()\n", "sparsity = (feature_activations == 0).float().mean().item()\n", "\n", "# Calculate Fraction of Variance Unexplained (FVU)\n", "residual = reconstructed - test_activations_f32\n", "total_variance = (test_activations_f32 - test_activations_f32.mean(dim=-1, keepdim=True)).pow(2).sum(dim=-1)\n", "residual_variance = residual.pow(2).sum(dim=-1)\n", "fvu = (residual_variance / (total_variance + 1e-8)).mean().item()\n", "\n", "print(f\"\\n📈 Quick Evaluation Results:\")\n", "print(f\"  MSE Loss: {sae_output.mse_loss.item():.6f}\")\n", "print(f\"  L1 Loss: {sae_output.l1_loss.item():.6f}\")\n", "print(f\"  FVU: {sae_output.fvu.item():.4f}\")\n", "print(f\"  Cosine Similarity: {cosine_sim:.4f}\")\n", "print(f\"  Sparsity: {sae_output.sparsity.item():.4f}\")\n", "print(f\"  Active Features: {(feature_activations > 0).sum(dim=1).float().mean():.1f} / {feature_activations.size(1)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Compare Different SAE Architectures\n", "\n", "Let's train and compare different SAE architectures to see which performs best."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train different SAE architectures for comparison\n", "architectures = [\"standard\", \"gated\", \"jumprelu\"]\n", "trained_saes = {}\n", "evaluation_results = {}\n", "\n", "for arch in architectures:\n", "    print(f\"\\n🏗️ Training {arch.upper()} SAE...\")\n", "\n", "    # Create config for this architecture\n", "    arch_config = config.copy()\n", "    arch_config.architecture = arch\n", "    # Use fewer tokens for comparison (faster training)\n", "    arch_config.training.total_training_tokens = 10_000_000  # 10M tokens\n", "\n", "    # Train SAE\n", "    trainer = SAETrainer(arch_config)\n", "    sae = trainer.train()\n", "\n", "    # Save SAE\n", "    save_path = f\"llama_3_1_8b_layer{target_layer}_{arch}_sae.pt\"\n", "    trainer.save_model(save_path)\n", "    trained_saes[arch] = sae\n", "\n", "    # Quick evaluation\n", "    with torch.no_grad():\n", "        test_activations_f32 = test_activations.float()\n", "        sae_output = sae(test_activations_f32)\n", "        reconstructed = sae_output.sae_out\n", "        feature_activations = sae_output.feature_acts\n", "\n", "    mse = sae_output.mse_loss.item()\n", "    cosine_sim = torch.nn.functional.cosine_similarity(\n", "        test_activations_f32.flatten(), reconstructed.flatten(), dim=0\n", "    ).item()\n", "    sparsity = sae_output.sparsity.item()\n", "\n", "    evaluation_results[arch] = {\n", "        'mse': mse,\n", "        'cosine_sim': cosine_sim,\n", "        'sparsity': sparsity,\n", "        'active_features': (feature_activations > 0).sum(dim=1).float().mean().item()\n", "    }\n", "\n", "    print(f\"  ✓ {arch.upper()} SAE completed\")\n", "    print(f\"    MSE: {mse:.6f}, Cosine Sim: {cosine_sim:.4f}, Sparsity: {sparsity:.4f}\")\n", "\n", "print(\"\\n🏆 Architecture Comparison Results:\")\n", "print(\"Architecture | MSE      | Cosine Sim | Sparsity | Active Features\")\n", "print(\"-\" * 65)\n", "for arch, results in evaluation_results.items():\n", "    print(f\"{arch:11} | {results['mse']:.6f} | {results['cosine_sim']:.6f} | {results['sparsity']:.6f} | {results['active_features']:.1f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Function Extraction\n", "\n", "Now let's extract behavioral functions from our trained SAE. We'll create a function that distinguishes between context-based and knowledge-based responses."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function extraction: Context vs Knowledge behavior\n", "print(\"🔍 Starting function extraction...\")\n", "\n", "# Use the best performing SAE (typically gated)\n", "best_sae = trained_saes['gated']\n", "\n", "# Create function extractor\n", "function_extractor = FunctionExtractor(\n", "    sae=best_sae,\n", "    initialization_method=\"uniform\",\n", "    regularization_strength=1e-5,\n", "    device=device\n", ")\n", "\n", "# Define examples for different behaviors\n", "context_based_prompts = [\n", "    \"Based on the context provided, the answer is clear.\",\n", "    \"According to the given information, we can conclude that.\",\n", "    \"The context clearly states that the solution is.\",\n", "    \"From the provided text, it's evident that.\",\n", "    \"The passage indicates that the correct answer is.\"\n", "]\n", "\n", "knowledge_based_prompts = [\n", "    \"From my knowledge, I believe the answer is.\",\n", "    \"Based on what I know, the solution should be.\",\n", "    \"Generally speaking, this type of problem requires.\",\n", "    \"In my understanding, the correct approach is.\",\n", "    \"From general knowledge, we can determine that.\"\n", "]\n", "\n", "# Get activations for both types of prompts\n", "def get_activations_for_prompts(prompts):\n", "    inputs = tokenizer(prompts, return_tensors=\"pt\", padding=True, truncation=True)\n", "    inputs = {k: v.to(device) for k, v in inputs.items()}\n", "\n", "    with torch.no_grad():\n", "        outputs = model(**inputs, output_hidden_states=True)\n", "        activations = outputs.hidden_states[target_layer]\n", "        # Take the last token activation for each sequence\n", "        activations = activations[:, -1, :]  # [batch_size, hidden_size]\n", "\n", "    return activations\n", "\n", "context_activations = get_activations_for_prompts(context_based_prompts)\n", "knowledge_activations = get_activations_for_prompts(knowledge_based_prompts)\n", "\n", "print(f\"Context activations shape: {context_activations.shape}\")\n", "print(f\"Knowledge activations shape: {knowledge_activations.shape}\")\n", "\n", "# Extract function\n", "print(\"\\n🎯 Extracting behavioral function...\")\n", "extraction_result = function_extractor.extract_function(\n", "    target_activations=context_activations,\n", "    context_activations=knowledge_activations,\n", "    learning_rate=1e-3,\n", "    num_iterations=1000,\n", "    verbose=True\n", ")\n", "\n", "print(f\"\\n✅ Function extraction completed!\")\n", "print(f\"  Active features: {len(extraction_result.active_features)}\")\n", "print(f\"  Extraction strength: {extraction_result.extraction_strength:.6f}\")\n", "print(f\"  Final loss: {extraction_result.metadata.get('final_loss', 'N/A'):.6f}\")\n", "\n", "# Analyze feature importance\n", "importance_stats = function_extractor.analyze_feature_importance()\n", "print(f\"\\n📊 Feature Importance Statistics:\")\n", "print(f\"  Mean importance: {importance_stats['mean']:.6f}\")\n", "print(f\"  Std importance: {importance_stats['std']:.6f}\")\n", "print(f\"  Max importance: {importance_stats['max']:.6f}\")\n", "\n", "# Get top features\n", "top_features = function_extractor.get_top_features(k=20)\n", "print(f\"\\n🔝 Top 20 Features: {top_features[:10]}...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Representation Engineering\n", "\n", "Now let's use our extracted function to create steering vectors and modify model behavior."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create representation engineer\n", "print(\"🎛️ Setting up representation engineering...\")\n", "\n", "engineer = RepresentationEngineer(\n", "    model=model,\n", "    tokenizer=tokenizer,\n", "    sae=best_sae,\n", "    hook_layer=target_layer,\n", "    hook_name=mlp_hook_name  # Use the same hook name as SAE training\n", ")\n", "\n", "# Create steering vector using our extracted function\n", "print(\"🧭 Creating steering vector...\")\n", "steering_vector = engineer.create_steering_vector(\n", "    positive_examples=context_based_prompts,\n", "    negative_examples=knowledge_based_prompts,\n", "    strength=2.0\n", ")\n", "\n", "print(f\"✓ Steering vector created with shape: {steering_vector.shape}\")\n", "\n", "# Test intervention on some prompts\n", "test_prompts = [\n", "    \"What is the capital of France?\",\n", "    \"How does photosynthesis work?\",\n", "    \"Explain machine learning in simple terms.\",\n", "    \"What are the benefits of renewable energy?\"\n", "]\n", "\n", "print(\"\\n🧪 Testing intervention effects...\")\n", "\n", "# Apply intervention\n", "intervention_fn = engineer.apply_steering_intervention(\n", "    steering_vector,\n", "    strength=1.5,\n", "    layers=[target_layer]\n", ")\n", "\n", "# Test intervention effectiveness\n", "results = engineer.test_intervention(\n", "    test_prompts,\n", "    intervention_fn,\n", "    max_new_tokens=50,\n", "    temperature=0.7\n", ")\n", "\n", "print(\"\\n📝 Intervention Results:\")\n", "print(\"=\" * 80)\n", "for i, (prompt, result) in enumerate(zip(test_prompts, results)):\n", "    print(f\"\\nPrompt {i+1}: {prompt}\")\n", "    print(f\"Original:  {result['original_text'][:100]}...\")\n", "    print(f\"Modified:  {result['modified_text'][:100]}...\")\n", "    print(\"-\" * 40)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 8: Comprehensive Evaluation\n", "\n", "Let's perform a comprehensive evaluation of our SAEs using SpARE's built-in evaluation tools."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive SAE evaluation\n", "print(\"📊 Starting comprehensive SAE evaluation...\")\n", "\n", "# Create evaluator\n", "evaluator = SAEEvaluator()\n", "\n", "# Prepare larger test dataset for evaluation\n", "eval_texts = [\n", "    \"The theory of relativity was developed by <PERSON>.\",\n", "    \"Machine learning algorithms can learn from data.\",\n", "    \"The human brain contains billions of neurons.\",\n", "    \"Climate change affects global weather patterns.\",\n", "    \"Quantum computers use quantum mechanical phenomena.\",\n", "    \"DNA contains the genetic instructions for life.\",\n", "    \"The internet connects computers worldwide.\",\n", "    \"Renewable energy sources include solar and wind.\",\n", "    \"Artificial intelligence mimics human intelligence.\",\n", "    \"The periodic table organizes chemical elements.\"\n", "]\n", "\n", "# Get evaluation activations\n", "eval_inputs = tokenizer(eval_texts, return_tensors=\"pt\", padding=True, truncation=True)\n", "eval_inputs = {k: v.to(device) for k, v in eval_inputs.items()}\n", "\n", "with torch.no_grad():\n", "    eval_outputs = model(**eval_inputs, output_hidden_states=True)\n", "    eval_activations = eval_outputs.hidden_states[target_layer]\n", "    eval_activations = eval_activations.view(-1, eval_activations.size(-1))\n", "\n", "print(f\"Evaluation activations shape: {eval_activations.shape}\")\n", "\n", "# Comprehensive evaluation for each SAE\n", "comprehensive_results = {}\n", "\n", "for arch_name, sae in trained_saes.items():\n", "    print(f\"\\n🔍 Evaluating {arch_name.upper()} SAE...\")\n", "\n", "    evaluation = evaluator.evaluate_sae_comprehensive(\n", "        sae=sae,\n", "        test_activations=eval_activations,\n", "        compute_feature_metrics=True,\n", "        compute_reconstruction_metrics=True,\n", "        compute_sparsity_metrics=True\n", "    )\n", "\n", "    comprehensive_results[arch_name] = evaluation\n", "\n", "    print(f\"  Overall Score: {evaluation.overall_score:.4f}\")\n", "    print(f\"  Reconstruction FVU: {evaluation.reconstruction_metrics['fvu']:.4f}\")\n", "    print(f\"  Sparsity: {evaluation.sparsity_metrics['overall_sparsity']:.4f}\")\n", "    print(f\"  Active Features: {evaluation.sparsity_metrics['active_features']}\")\n", "    print(f\"  Dead Features: {evaluation.sparsity_metrics['dead_features']}\")\n", "\n", "# Compare all SAEs\n", "print(\"\\n🏆 Final SAE Comparison:\")\n", "print(\"=\" * 80)\n", "print(f\"{'Architecture':<12} {'Overall Score':<14} {'FVU':<8} {'Sparsity':<10} {'Active Features':<15}\")\n", "print(\"-\" * 80)\n", "\n", "for arch_name, evaluation in comprehensive_results.items():\n", "    print(f\"{arch_name:<12} {evaluation.overall_score:<14.4f} \"\n", "          f\"{evaluation.reconstruction_metrics['fvu']:<8.4f} \"\n", "          f\"{evaluation.sparsity_metrics['overall_sparsity']:<10.4f} \"\n", "          f\"{evaluation.sparsity_metrics['active_features']:<15}\")\n", "\n", "# Find best performing SAE\n", "best_arch = max(comprehensive_results.keys(),\n", "                key=lambda x: comprehensive_results[x].overall_score)\n", "print(f\"\\n🥇 Best performing architecture: {best_arch.upper()}\")\n", "print(f\"   Score: {comprehensive_results[best_arch].overall_score:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 9: Visualization and Analysis\n", "\n", "Let's create comprehensive visualizations to understand our SAE training results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create visualizations\n", "print(\"📈 Creating visualizations...\")\n", "\n", "# Initialize visualizer\n", "visualizer = SAEVisualizer()\n", "\n", "# Set up matplotlib for better plots\n", "plt.style.use('default')\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "plt.rcParams['font.size'] = 10\n", "\n", "# 1. Plot activation distributions\n", "fig1 = visualizer.plot_activation_distribution(\n", "    activations=eval_activations.cpu().numpy(),\n", "    title=f\"LLaMA 3.1 8B Layer {target_layer} Activation Distribution\",\n", "    save_path=\"activation_distribution.png\"\n", ")\n", "plt.show()\n", "\n", "# 2. Plot feature importance from function extraction\n", "if 'extraction_result' in locals():\n", "    fig2 = visualizer.plot_feature_importance(\n", "        importance_scores=extraction_result.feature_weights.cpu().numpy(),\n", "        top_k=50,\n", "        title=\"Top 50 Features: Context vs Knowledge Behavior\",\n", "        save_path=\"feature_importance.png\"\n", "    )\n", "    plt.show()\n", "\n", "# 3. Plot SAE architecture comparison\n", "comparison_data = {\n", "    arch: {\n", "        'overall_score': results.overall_score,\n", "        'fvu': results.reconstruction_metrics['fvu'],\n", "        'sparsity': results.sparsity_metrics['overall_sparsity']\n", "    }\n", "    for arch, results in comprehensive_results.items()\n", "}\n", "\n", "fig3 = visualizer.plot_sae_comparison(\n", "    comparison_results=comparison_data,\n", "    metrics=[\"overall_score\", \"fvu\", \"sparsity\"],\n", "    title=\"SAE Architecture Comparison on LLaMA 3.1 8B\",\n", "    save_path=\"sae_comparison.png\"\n", ")\n", "plt.show()\n", "\n", "# 4. Create feature activation heatmap for best SAE\n", "best_sae_obj = trained_saes[best_arch]\n", "with torch.no_grad():\n", "    sample_output = best_sae_obj(eval_activations[:10])  # First 10 samples\n", "    feature_acts = sample_output.feature_acts.cpu().numpy()\n", "\n", "# Plot heatmap of top active features\n", "top_features_mask = np.sum(feature_acts > 0, axis=0) > 0  # Features active in at least one sample\n", "active_features = feature_acts[:, top_features_mask]\n", "\n", "if active_features.shape[1] > 0:\n", "    plt.figure(figsize=(15, 8))\n", "    plt.imshow(active_features[:, :min(100, active_features.shape[1])].T,\n", "               aspect='auto', cmap='viridis', interpolation='nearest')\n", "    plt.colorbar(label='Feature Activation')\n", "    plt.title(f'Feature Activation Heatmap - {best_arch.upper()} SAE')\n", "    plt.xlabel('Sample Index')\n", "    plt.ylabel('Feature Index')\n", "    plt.tight_layout()\n", "    plt.savefig('feature_heatmap.png', dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "\n", "print(\"✅ All visualizations created and saved!\")\n", "print(\"📁 Saved files:\")\n", "print(\"  - activation_distribution.png\")\n", "print(\"  - feature_importance.png\")\n", "print(\"  - sae_comparison.png\")\n", "print(\"  - feature_heatmap.png\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 10: <PERSON><PERSON><PERSON> and Next Steps\n", "\n", "Congratulations! You've completed the comprehensive SpARE tutorial. Let's summarize what we accomplished."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Tutorial summary\n", "print(\"🎉 Tutorial Complete! Here's what we accomplished:\")\n", "print(\"=\" * 60)\n", "\n", "print(\"\\n✅ 1. Model Setup:\")\n", "print(f\"   - Loaded LLaMA 3.1 8B Instruct ({model_info['total_parameters']:,} parameters)\")\n", "print(f\"   - Explored model architecture and hook points\")\n", "print(f\"   - Selected layer {target_layer} for SAE training\")\n", "\n", "print(\"\\n✅ 2. Dataset Preparation:\")\n", "print(f\"   - Processed WikiText-2 dataset\")\n", "print(f\"   - Tokenized and chunked text data\")\n", "print(f\"   - Prepared {dataset_info.get('processed_size', 'N/A')} training examples\")\n", "\n", "print(\"\\n✅ 3. SAE Training:\")\n", "print(f\"   - Trained 3 different SAE architectures: Standard, Gated, JumpReLU\")\n", "print(f\"   - Used {config.expansion_factor}x expansion factor\")\n", "print(f\"   - Created {model_info['hidden_size'] * config.expansion_factor:,} SAE features\")\n", "\n", "print(\"\\n✅ 4. Function Extraction:\")\n", "if 'extraction_result' in locals():\n", "    print(f\"   - Extracted behavioral function with {len(extraction_result.active_features)} active features\")\n", "    print(f\"   - Achieved extraction strength: {extraction_result.extraction_strength:.6f}\")\n", "else:\n", "    print(\"   - Function extraction framework demonstrated\")\n", "\n", "print(\"\\n✅ 5. Representation Engineering:\")\n", "print(\"   - Created steering vectors for behavior modification\")\n", "print(\"   - Tested intervention effects on model outputs\")\n", "print(\"   - Demonstrated context vs knowledge steering\")\n", "\n", "print(\"\\n✅ 6. Comprehensive Evaluation:\")\n", "if comprehensive_results:\n", "    print(f\"   - Best architecture: {best_arch.upper()}\")\n", "    print(f\"   - Best score: {comprehensive_results[best_arch].overall_score:.4f}\")\n", "    print(\"   - Compared reconstruction quality, sparsity, and feature metrics\")\n", "\n", "print(\"\\n✅ 7. Visualization:\")\n", "print(\"   - Created activation distribution plots\")\n", "print(\"   - Visualized feature importance\")\n", "print(\"   - Generated architecture comparison charts\")\n", "print(\"   - Produced feature activation heatmaps\")\n", "\n", "print(\"\\n📁 Generated Files:\")\n", "saved_files = [\n", "    f\"llama_3_1_8b_layer{target_layer}_gated_sae.pt\",\n", "    f\"llama_3_1_8b_layer{target_layer}_standard_sae.pt\",\n", "    f\"llama_3_1_8b_layer{target_layer}_jumprelu_sae.pt\",\n", "    \"activation_distribution.png\",\n", "    \"feature_importance.png\",\n", "    \"sae_comparison.png\",\n", "    \"feature_heatmap.png\"\n", "]\n", "\n", "for file in saved_files:\n", "    print(f\"   - {file}\")\n", "\n", "print(\"\\n🚀 Next Steps:\")\n", "print(\"   1. Experiment with different models (GPT, BERT, Mistral, etc.)\")\n", "print(\"   2. Try different datasets (code, scientific, domain-specific)\")\n", "print(\"   3. Explore different hook points (attention, MLP, residual)\")\n", "print(\"   4. Scale up training with more tokens and larger expansion factors\")\n", "print(\"   5. Implement custom SAE architectures\")\n", "print(\"   6. Apply to real-world representation engineering tasks\")\n", "print(\"   7. Contribute to the SpARE library development\")\n", "\n", "print(\"\\n📚 Resources:\")\n", "print(\"   - SpARE Documentation: Check the docs/ folder\")\n", "print(\"   - Example <PERSON>: See demo.py for more examples\")\n", "print(\"   - Research Paper: https://arxiv.org/pdf/2410.15999\")\n", "print(\"   - GitHub Issues: Report bugs and request features\")\n", "\n", "print(\"\\n🎯 Happy SAE Training with SpARE! 🎯\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "## 🎓 Tutorial Complete!\n", "\n", "You've successfully completed the comprehensive SpARE tutorial! You now have hands-on experience with:\n", "\n", "- **Universal Model Loading**: Working with any HuggingFace model\n", "- **SAE Training**: Training different architectures on real data\n", "- **Function Extraction**: Extracting behavioral functions from SAEs\n", "- **Representation Engineering**: Modifying model behavior with steering vectors\n", "- **Comprehensive Evaluation**: Assessing SAE quality with multiple metrics\n", "- **Visualization**: Creating insightful plots and analysis\n", "\n", "### 🔬 Research Applications\n", "\n", "With these skills, you can now:\n", "- Investigate mechanistic interpretability questions\n", "- Build representation engineering applications\n", "- Conduct comparative studies across models\n", "- Develop new SAE architectures and techniques\n", "\n", "### 🤝 Contributing\n", "\n", "We welcome contributions to SpARE! Consider:\n", "- Adding support for new model architectures\n", "- Implementing novel SAE variants\n", "- Improving evaluation metrics\n", "- Creating additional tutorials and examples\n", "\n", "### 📞 Support\n", "\n", "If you have questions or need help:\n", "- Check the documentation in the `docs/` folder\n", "- Review `demo.py` for additional examples\n", "- Open an issue on GitHub\n", "- Join our community discussions\n", "\n", "**Thank you for using SpARE! 🌟**"]}], "metadata": {"kernelspec": {"display_name": "itas", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}