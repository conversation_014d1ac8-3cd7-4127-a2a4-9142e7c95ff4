{"time":"2025-06-05T03:30:24.340308396+09:00","level":"INFO","msg":"stream: starting","core version":"0.20.0","symlink path":"/data_x/junkim100/projects/scheming_sae/itas/wandb/run-20250605_033023-wfy1byqn/logs/debug-core.log"}
{"time":"2025-06-05T03:30:24.770548875+09:00","level":"INFO","msg":"stream: created new stream","id":"wfy1byqn"}
{"time":"2025-06-05T03:30:24.770655287+09:00","level":"INFO","msg":"stream: started","id":"wfy1byqn"}
{"time":"2025-06-05T03:30:24.770676958+09:00","level":"INFO","msg":"writer: Do: started","stream_id":"wfy1byqn"}
{"time":"2025-06-05T03:30:24.770738955+09:00","level":"INFO","msg":"handler: started","stream_id":"wfy1byqn"}
{"time":"2025-06-05T03:30:24.770734577+09:00","level":"INFO","msg":"sender: started","stream_id":"wfy1byqn"}
{"time":"2025-06-05T03:30:25.108175309+09:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-06-05T05:53:10.686500543+09:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/files/junkim/itas-sae-training/wfy1byqn/file_stream","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
