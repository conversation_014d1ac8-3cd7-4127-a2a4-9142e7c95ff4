2025-06-05 03:30:23,679 INFO    MainThread:364186 [wandb_setup.py:_flush():81] Current SDK version is 0.20.0
2025-06-05 03:30:23,679 INFO    MainThread:364186 [wandb_setup.py:_flush():81] Configure stats pid to 364186
2025-06-05 03:30:23,679 INFO    MainThread:364186 [wandb_setup.py:_flush():81] Loading settings from /mnt/raid6/junkim100/.config/wandb/settings
2025-06-05 03:30:23,680 INFO    MainThread:364186 [wandb_setup.py:_flush():81] Loading settings from /data_x/junkim100/projects/scheming_sae/itas/wandb/settings
2025-06-05 03:30:23,680 INFO    MainThread:364186 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-06-05 03:30:23,680 INFO    MainThread:364186 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /data_x/junkim100/projects/scheming_sae/itas/wandb/run-20250605_033023-wfy1byqn/logs/debug.log
2025-06-05 03:30:23,680 INFO    MainThread:364186 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /data_x/junkim100/projects/scheming_sae/itas/wandb/run-20250605_033023-wfy1byqn/logs/debug-internal.log
2025-06-05 03:30:23,680 INFO    MainThread:364186 [wandb_init.py:init():831] calling init triggers
2025-06-05 03:30:23,680 INFO    MainThread:364186 [wandb_init.py:init():836] wandb.init called with sweep_config: {}
config: {'model_name': 'meta-llama/Llama-3.1-8B-Instruct', 'dataset_name': 'togethercomputer/RedPajama-Data-1T-Sample', 'architecture': 'gated', 'expansion_factor': 32, 'hook_layer': 16, 'hook_name': 'layers.16.mlp', 'd_in': 4096, 'd_sae': 131072, 'total_training_tokens': 50000000, 'batch_size': 16384, 'learning_rate': 0.0003, 'l1_coefficient': 0.001, 'num_gpus': 8, 'gpu_ids': [0, 1, 2, 3, 4, 5, 6, 7], '_wandb': {}}
2025-06-05 03:30:23,680 INFO    MainThread:364186 [wandb_init.py:init():872] starting backend
2025-06-05 03:30:24,119 INFO    MainThread:364186 [wandb_init.py:init():875] sending inform_init request
2025-06-05 03:30:24,333 INFO    MainThread:364186 [wandb_init.py:init():883] backend started and connected
2025-06-05 03:30:24,347 INFO    MainThread:364186 [wandb_init.py:init():956] updated telemetry
2025-06-05 03:30:24,577 INFO    MainThread:364186 [wandb_init.py:init():980] communicating run to backend with 90.0 second timeout
