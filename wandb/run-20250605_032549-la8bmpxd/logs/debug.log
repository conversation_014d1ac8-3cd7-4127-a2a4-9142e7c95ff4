2025-06-05 03:25:49,423 INFO    MainThread:361148 [wandb_setup.py:_flush():81] Current SDK version is 0.20.0
2025-06-05 03:25:49,423 INFO    MainThread:361148 [wandb_setup.py:_flush():81] Configure stats pid to 361148
2025-06-05 03:25:49,423 INFO    MainThread:361148 [wandb_setup.py:_flush():81] Loading settings from /mnt/raid6/junkim100/.config/wandb/settings
2025-06-05 03:25:49,423 INFO    MainThread:361148 [wandb_setup.py:_flush():81] Loading settings from /data_x/junkim100/projects/scheming_sae/itas/wandb/settings
2025-06-05 03:25:49,423 INFO    MainThread:361148 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-06-05 03:25:49,424 INFO    MainThread:361148 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /data_x/junkim100/projects/scheming_sae/itas/wandb/run-20250605_032549-la8bmpxd/logs/debug.log
2025-06-05 03:25:49,424 INFO    MainThread:361148 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /data_x/junkim100/projects/scheming_sae/itas/wandb/run-20250605_032549-la8bmpxd/logs/debug-internal.log
2025-06-05 03:25:49,424 INFO    MainThread:361148 [wandb_init.py:init():831] calling init triggers
2025-06-05 03:25:49,424 INFO    MainThread:361148 [wandb_init.py:init():836] wandb.init called with sweep_config: {}
config: {'model_name': 'meta-llama/Llama-3.1-8B-Instruct', 'dataset_name': 'togethercomputer/RedPajama-Data-1T-Sample', 'architecture': 'gated', 'expansion_factor': 32, 'hook_layer': 16, 'hook_name': 'layers.16.mlp', 'd_in': 4096, 'd_sae': 131072, 'total_training_tokens': 50000000, 'batch_size': 16384, 'learning_rate': 0.0003, 'l1_coefficient': 0.001, 'num_gpus': 8, 'gpu_ids': [0, 1, 2, 3, 4, 5, 6, 7], '_wandb': {}}
2025-06-05 03:25:49,424 INFO    MainThread:361148 [wandb_init.py:init():872] starting backend
2025-06-05 03:25:49,757 INFO    MainThread:361148 [wandb_init.py:init():875] sending inform_init request
2025-06-05 03:25:49,920 INFO    MainThread:361148 [wandb_init.py:init():883] backend started and connected
2025-06-05 03:25:49,929 INFO    MainThread:361148 [wandb_init.py:init():956] updated telemetry
2025-06-05 03:25:50,074 INFO    MainThread:361148 [wandb_init.py:init():980] communicating run to backend with 90.0 second timeout
2025-06-05 03:25:50,763 INFO    MainThread:361148 [wandb_init.py:init():1032] starting run threads in backend
2025-06-05 03:25:50,888 INFO    MainThread:361148 [wandb_run.py:_console_start():2453] atexit reg
2025-06-05 03:25:50,888 INFO    MainThread:361148 [wandb_run.py:_redirect():2301] redirect: wrap_raw
2025-06-05 03:25:50,889 INFO    MainThread:361148 [wandb_run.py:_redirect():2370] Wrapping output streams.
2025-06-05 03:25:50,890 INFO    MainThread:361148 [wandb_run.py:_redirect():2393] Redirects installed.
2025-06-05 03:25:50,892 INFO    MainThread:361148 [wandb_init.py:init():1078] run started, returning control to user process
