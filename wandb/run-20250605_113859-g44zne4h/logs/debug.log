2025-06-05 11:38:59,497 INFO    MainThread:405874 [wandb_setup.py:_flush():81] Current SDK version is 0.20.0
2025-06-05 11:38:59,497 INFO    MainThread:405874 [wandb_setup.py:_flush():81] Configure stats pid to 405874
2025-06-05 11:38:59,497 INFO    MainThread:405874 [wandb_setup.py:_flush():81] Loading settings from /mnt/raid6/junkim100/.config/wandb/settings
2025-06-05 11:38:59,497 INFO    MainThread:405874 [wandb_setup.py:_flush():81] Loading settings from /data_x/junkim100/projects/scheming_sae/itas/wandb/settings
2025-06-05 11:38:59,497 INFO    MainThread:405874 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-06-05 11:38:59,497 INFO    MainThread:405874 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /data_x/junkim100/projects/scheming_sae/itas/wandb/run-20250605_113859-g44zne4h/logs/debug.log
2025-06-05 11:38:59,497 INFO    MainThread:405874 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /data_x/junkim100/projects/scheming_sae/itas/wandb/run-20250605_113859-g44zne4h/logs/debug-internal.log
2025-06-05 11:38:59,498 INFO    MainThread:405874 [wandb_init.py:init():831] calling init triggers
2025-06-05 11:38:59,498 INFO    MainThread:405874 [wandb_init.py:init():836] wandb.init called with sweep_config: {}
config: {'model_name': 'meta-llama/Llama-3.1-8B-Instruct', 'dataset_name': 'togethercomputer/RedPajama-Data-1T-Sample', 'architecture': 'gated', 'expansion_factor': 32, 'hook_layer': 16, 'hook_name': 'layers.16.mlp', 'd_in': 4096, 'd_sae': 131072, 'total_training_tokens': 50000000, 'batch_size': 8192, 'learning_rate': 0.0003, 'l1_coefficient': 0.001, 'num_gpus': 4, 'gpu_ids': [0, 1, 2, 3], '_wandb': {}}
2025-06-05 11:38:59,498 INFO    MainThread:405874 [wandb_init.py:init():872] starting backend
2025-06-05 11:38:59,892 INFO    MainThread:405874 [wandb_init.py:init():875] sending inform_init request
2025-06-05 11:39:00,044 INFO    MainThread:405874 [wandb_init.py:init():883] backend started and connected
2025-06-05 11:39:00,054 INFO    MainThread:405874 [wandb_init.py:init():956] updated telemetry
2025-06-05 11:39:00,204 INFO    MainThread:405874 [wandb_init.py:init():980] communicating run to backend with 90.0 second timeout
2025-06-05 11:39:00,835 INFO    MainThread:405874 [wandb_init.py:init():1032] starting run threads in backend
2025-06-05 11:39:00,942 INFO    MainThread:405874 [wandb_run.py:_console_start():2453] atexit reg
2025-06-05 11:39:00,942 INFO    MainThread:405874 [wandb_run.py:_redirect():2301] redirect: wrap_raw
2025-06-05 11:39:00,943 INFO    MainThread:405874 [wandb_run.py:_redirect():2370] Wrapping output streams.
2025-06-05 11:39:00,943 INFO    MainThread:405874 [wandb_run.py:_redirect():2393] Redirects installed.
2025-06-05 11:39:00,946 INFO    MainThread:405874 [wandb_init.py:init():1078] run started, returning control to user process
