"""
Improved utilities for SpARE library.

This module provides enhanced utility functions for model loading,
configuration validation, logging setup, and general helper functions.
"""

import os
import json
import logging
import warnings
from typing import Optional, Dict, Any, Tuple, Union, List
from pathlib import Path
import torch
from transformers import AutoTokenizer, AutoModel, PreTrainedModel, PreTrainedTokenizer

try:
    from transformers import LlamaForCausalLM, Gemma2ForCausalLM
except ImportError:
    # Fallback for older transformers versions
    LlamaForCausalLM = None
    Gemma2ForCausalLM = None

from .core.config import SAEConfig, ModelConfig
from .core.model_loader import UniversalModelLoader

# Project directory
PROJ_DIR = Path(os.environ.get("PROJ_DIR", "./"))


def setup_logging(
    level: Union[str, int] = logging.INFO,
    format_string: Optional[str] = None,
    log_file: Optional[str] = None,
) -> logging.Logger:
    """
    Setup logging configuration for SpARE.

    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR)
        format_string: Custom format string for log messages
        log_file: Optional file to write logs to

    Returns:
        Configured logger instance
    """
    if format_string is None:
        format_string = "%(asctime)s - %(levelname)s %(name)s %(lineno)s: %(message)s"

    # Configure root logger
    logging.basicConfig(
        level=level,
        format=format_string,
        datefmt="%m/%d/%Y %H:%M:%S",
    )

    logger = logging.getLogger("spare")
    logger.setLevel(level)

    # Add file handler if specified
    if log_file is not None:
        add_file_handler(logger, os.path.dirname(log_file), os.path.basename(log_file))

    return logger


def add_file_handler(logger: logging.Logger, output_dir: str, file_name: str) -> None:
    """
    Add file handler to logger.

    Args:
        logger: Logger instance
        output_dir: Directory to save log file
        file_name: Name of log file
    """
    os.makedirs(output_dir, exist_ok=True)
    file_handler = logging.FileHandler(os.path.join(output_dir, file_name))
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(
        logging.Formatter(
            "%(asctime)s - %(levelname)s %(name)s %(lineno)s: %(message)s"
        )
    )
    logger.addHandler(file_handler)


def load_jsonl(path: Union[str, Path]) -> List[Dict[str, Any]]:
    """
    Load data from JSONL file.

    Args:
        path: Path to JSONL file

    Returns:
        List of dictionaries from JSONL file
    """
    with open(path, "r", encoding="utf-8") as f:
        return [json.loads(line.strip()) for line in f if line.strip()]


def save_jsonl(data: List[Dict[str, Any]], path: Union[str, Path]) -> None:
    """
    Save data to JSONL file.

    Args:
        data: List of dictionaries to save
        path: Path to save JSONL file
    """
    with open(path, "w", encoding="utf-8") as f:
        for item in data:
            f.write(json.dumps(item) + "\n")


def _legacy_load_model(model_path, flash_attn, not_return_model=False):
    """Legacy model loading function - internal use only."""
    tokenizer = AutoTokenizer.from_pretrained(
        model_path,
        padding_side="left",
        truncation_side="left",
    )
    tokenizer.pad_token = tokenizer.eos_token
    attn_implementation = "flash_attention_2" if flash_attn else "eager"
    print(f"attn_implementation = {attn_implementation}")
    if not_return_model:
        model = None
    else:
        if "gemma" in model_path.lower() and Gemma2ForCausalLM is not None:
            model = Gemma2ForCausalLM.from_pretrained(
                model_path,
                attn_implementation=attn_implementation,
                torch_dtype=torch.bfloat16,
            )
        elif LlamaForCausalLM is not None:
            model = LlamaForCausalLM.from_pretrained(
                model_path,
                attn_implementation=attn_implementation,
                torch_dtype=torch.bfloat16,
            )
        else:
            raise ImportError("Required model classes not available")
        model.cuda().eval()
    return model, tokenizer


def _legacy_init_frozen_language_model(model_path, attn_imp="flash_attention_2"):
    """Legacy frozen model loading function - internal use only."""
    bf16 = torch.bfloat16
    if "llama" in model_path.lower() and LlamaForCausalLM is not None:
        model = LlamaForCausalLM.from_pretrained(
            model_path, attn_implementation=attn_imp, torch_dtype=bf16
        )
    elif "gemma" in model_path and Gemma2ForCausalLM is not None:
        model = Gemma2ForCausalLM.from_pretrained(
            model_path, attn_implementation=attn_imp, torch_dtype=bf16
        )
    else:
        raise NotImplementedError("Model type not supported or classes not available")
    model.cuda().eval()
    for pn, p in model.named_parameters():
        p.requires_grad = False
    tokenizer = AutoTokenizer.from_pretrained(
        model_path,
        padding_side="left",
        truncation_side="left",
    )
    tokenizer.pad_token = tokenizer.eos_token
    return model, tokenizer


def load_model_and_tokenizer(
    model_name: str,
    use_flash_attention: bool = True,
    torch_dtype: Union[str, torch.dtype] = "auto",
    device_map: str = "auto",
    **kwargs,
) -> Tuple[PreTrainedModel, PreTrainedTokenizer]:
    """
    Load model and tokenizer using the universal loader.

    Args:
        model_name: HuggingFace model name or path
        use_flash_attention: Whether to use Flash Attention 2
        torch_dtype: Data type for model weights
        device_map: Device mapping strategy
        **kwargs: Additional arguments for model loading

    Returns:
        Tuple of (model, tokenizer)
    """
    # Create model config
    model_config = ModelConfig(
        model_name=model_name,
        use_flash_attention=use_flash_attention,
        torch_dtype=torch_dtype,
        device_map=device_map,
        **kwargs,
    )

    # Load using universal loader
    loader = UniversalModelLoader(model_config)
    return loader.load_model_and_tokenizer()


def validate_config(config: SAEConfig) -> List[str]:
    """
    Validate SAE configuration and return list of issues.

    Args:
        config: SAE configuration to validate

    Returns:
        List of validation error messages (empty if valid)
    """
    issues = []

    # Validate model configuration
    if not config.model.model_name:
        issues.append("Model name is required")

    # Validate dataset configuration
    if not config.dataset.dataset_name:
        issues.append("Dataset name is required")

    if config.dataset.max_seq_length <= 0:
        issues.append("Max sequence length must be positive")

    # Validate SAE architecture
    if config.d_in is not None and config.d_in <= 0:
        issues.append("d_in must be positive")

    if config.expansion_factor <= 0:
        issues.append("Expansion factor must be positive")

    if config.d_sae is not None and config.d_sae <= 0:
        issues.append("d_sae must be positive")

    # Validate training configuration
    if config.training.total_training_tokens <= 0:
        issues.append("Total training tokens must be positive")

    if config.training.batch_size <= 0:
        issues.append("Batch size must be positive")

    if config.training.learning_rate <= 0:
        issues.append("Learning rate must be positive")

    if config.training.l1_coefficient < 0:
        issues.append("L1 coefficient must be non-negative")

    # Validate hook configuration
    if config.hook_layer < 0:
        issues.append("Hook layer must be non-negative")

    return issues


def create_sae_config(
    model_name: str = "microsoft/DialoGPT-medium",
    dataset_name: str = "wikitext",
    hook_layer: int = 6,
    expansion_factor: int = 32,
    architecture: str = "standard",
) -> SAEConfig:
    """
    Create a comprehensive SAE configuration for any HuggingFace model.

    Args:
        model_name: HuggingFace model identifier
        dataset_name: HuggingFace dataset identifier
        hook_layer: Layer to extract activations from
        expansion_factor: SAE expansion factor
        architecture: SAE architecture type

    Returns:
        Complete SAE configuration
    """
    import torch
    from .core.config import DatasetConfig, TrainingConfig

    # Model configuration
    model_config = ModelConfig(
        model_name=model_name,
        use_flash_attention=True,  # Will be disabled automatically for incompatible models
        torch_dtype="bfloat16",
        trust_remote_code=False,
    )

    # Dataset configuration
    dataset_config = DatasetConfig(
        dataset_name=dataset_name,
        dataset_split="train",
        text_column="text",
        max_seq_length=2048,
        chunk_size=2048,
        streaming=False,
        dataset_kwargs=(
            {"name": "wikitext-2-raw-v1"} if dataset_name == "wikitext" else {}
        ),
    )

    # Training configuration
    training_config = TrainingConfig(
        total_training_tokens=1_000_000,  # Smaller for demo
        batch_size=2048,
        learning_rate=3e-4,
        l1_coefficient=1e-3,
        lr_scheduler="cosine",
        log_every_n_steps=50,
        eval_every_n_tokens=50_000,
        checkpoint_every_n_tokens=100_000,
        use_wandb=False,  # Disable for demo
    )

    # Load model to get correct hook names
    from itas.core.model_loader import UniversalModelLoader

    model_loader = UniversalModelLoader(model_config)
    model, tokenizer = model_loader.load_model_and_tokenizer()

    # Get correct hook names for this model
    hook_names = model_loader.get_hook_names()
    mlp_hook_pattern = hook_names["mlp_out"]

    # Main SAE configuration
    config = SAEConfig(
        model=model_config,
        dataset=dataset_config,
        training=training_config,
        architecture=architecture,
        expansion_factor=expansion_factor,
        hook_layer=hook_layer,
        hook_name=mlp_hook_pattern,  # Use correct hook pattern for this model
        activation_fn="relu",
        normalize_decoder=True,
        prepend_bos=True,
        device="cuda" if torch.cuda.is_available() else "cpu",
        dtype="float32",
        seed=42,
    )

    return config


# Legacy function compatibility (deprecated)
def load_model(model_path: str, flash_attn: bool, not_return_model: bool = False):
    """
    Legacy model loading function (deprecated).

    Use load_model_and_tokenizer() instead.
    """
    warnings.warn(
        "load_model() is deprecated. Use load_model_and_tokenizer() instead.",
        DeprecationWarning,
        stacklevel=2,
    )

    return _legacy_load_model(model_path, flash_attn, not_return_model)


def init_frozen_language_model(model_path: str, attn_imp: str = "flash_attention_2"):
    """
    Legacy function for loading frozen models (deprecated).

    Use load_model_and_tokenizer() instead.
    """
    warnings.warn(
        "init_frozen_language_model() is deprecated. Use load_model_and_tokenizer() instead.",
        DeprecationWarning,
        stacklevel=2,
    )

    return _legacy_init_frozen_language_model(model_path, attn_imp)


def load_frozen_sae(layer_idx: int, model_name: str):
    """
    Legacy function for loading pre-trained SAEs (deprecated).

    This function is kept for backward compatibility but should be replaced
    with the new SAE loading mechanisms in the core module.
    """
    warnings.warn(
        "load_frozen_sae() is deprecated. Use the new SAE loading mechanisms instead.",
        DeprecationWarning,
        stacklevel=2,
    )

    # Import here to avoid circular imports
    try:
        from itas.sae import Sae
        from itas.sae_lens.eleuther_sae_wrapper import EleutherSae
    except ImportError:
        raise ImportError("Legacy SAE loading requires old SAE modules")

    if model_name == "Meta-Llama-3-8B":
        sae = Sae.load_from_hub(
            "EleutherAI/sae-llama-3-8b-32x", hookpoint=f"layers.{layer_idx}"
        )
    elif model_name == "Llama-2-7b-hf":
        sae = Sae.load_from_hub(
            "yuzhaouoe/Llama2-7b-SAE", hookpoint=f"layers.{layer_idx}"
        )
    elif model_name == "gemma-2-9b":
        sae, cfg_dict, sparsity = EleutherSae.from_pretrained(
            release="gemma-scope-9b-pt-res-canonical",
            sae_id=f"layer_{layer_idx}/width_131k/canonical",
            device="cuda",
        )
    else:
        raise NotImplementedError(f"sae for {model_name}")

    for pn, p in sae.named_parameters():
        p.requires_grad = False
    sae.cuda()
    return sae
