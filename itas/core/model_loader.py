"""
Universal model loader for any HuggingFace transformer model.

This module provides a unified interface for loading and configuring
transformer models from HuggingFace, with automatic detection of model
architecture and optimal settings.
"""

import logging
from typing import Tuple, Optional, Dict, Any, Union
import torch
from transformers import (
    AutoModel,
    AutoTokenizer,
    AutoConfig,
    BitsAndBytesConfig,
    PreTrainedModel,
    PreTrainedTokenizer,
)
from transformers.utils import is_flash_attn_2_available
import warnings

from .config import ModelConfig

logger = logging.getLogger(__name__)


class UniversalModelLoader:
    """
    Universal loader for HuggingFace transformer models.

    Automatically detects model architecture and applies optimal configurations
    for SAE training and inference.
    """

    def __init__(self, config: ModelConfig):
        """
        Initialize the model loader.

        Args:
            config: Model configuration specifying loading parameters
        """
        self.config = config
        self._model = None
        self._tokenizer = None
        self._model_config = None

    def load_model_and_tokenizer(self) -> <PERSON><PERSON>[PreTrainedModel, PreTrainedTokenizer]:
        """
        Load model and tokenizer with automatic configuration.

        Returns:
            Tuple of (model, tokenizer)

        Raises:
            ValueError: If model loading fails
            RuntimeError: If device setup fails
        """
        try:
            logger.info(f"Loading model: {self.config.model_name}")

            # Load model configuration first
            self._model_config = self._load_model_config()

            # Load tokenizer
            self._tokenizer = self._load_tokenizer()

            # Load model with optimized settings
            self._model = self._load_model()

            # Apply post-loading optimizations
            self._optimize_model()

            logger.info(f"Successfully loaded model: {self.config.model_name}")
            logger.info(f"Model architecture: {self._model_config.architectures}")
            logger.info(f"Model parameters: {self._count_parameters():,}")

            return self._model, self._tokenizer

        except Exception as e:
            logger.error(f"Failed to load model {self.config.model_name}: {str(e)}")
            raise ValueError(f"Model loading failed: {str(e)}") from e

    def _load_model_config(self) -> AutoConfig:
        """Load and configure model configuration."""
        config = AutoConfig.from_pretrained(
            self.config.model_name,
            revision=self.config.model_revision,
            trust_remote_code=self.config.trust_remote_code,
        )

        # Override max position embeddings if specified
        if self.config.max_position_embeddings is not None:
            if hasattr(config, "max_position_embeddings"):
                config.max_position_embeddings = self.config.max_position_embeddings
            elif hasattr(config, "n_positions"):
                config.n_positions = self.config.max_position_embeddings

        return config

    def _load_tokenizer(self) -> PreTrainedTokenizer:
        """Load and configure tokenizer."""
        tokenizer = AutoTokenizer.from_pretrained(
            self.config.model_name,
            revision=self.config.model_revision,
            trust_remote_code=self.config.trust_remote_code,
            padding_side="left",
            truncation_side="left",
        )

        # Set pad token if not present
        if tokenizer.pad_token is None:
            if tokenizer.eos_token is not None:
                tokenizer.pad_token = tokenizer.eos_token
            else:
                # Add a pad token if neither exists
                tokenizer.add_special_tokens({"pad_token": "[PAD]"})

        return tokenizer

    def _load_model(self) -> PreTrainedModel:
        """Load model with optimized configuration."""
        # Prepare loading arguments
        model_kwargs = {
            "config": self._model_config,
            "revision": self.config.model_revision,
            "trust_remote_code": self.config.trust_remote_code,
            "torch_dtype": self.config.torch_dtype,
            "device_map": self.config.device_map,
            **self.config.model_kwargs,
        }

        # Configure quantization if requested
        if self.config.load_in_8bit or self.config.load_in_4bit:
            quantization_config = BitsAndBytesConfig(
                load_in_8bit=self.config.load_in_8bit,
                load_in_4bit=self.config.load_in_4bit,
            )
            model_kwargs["quantization_config"] = quantization_config

        # Configure attention implementation
        if self.config.use_flash_attention and is_flash_attn_2_available():
            model_kwargs["attn_implementation"] = "flash_attention_2"
            logger.info("Using Flash Attention 2")
        else:
            model_kwargs["attn_implementation"] = "eager"
            if self.config.use_flash_attention:
                warnings.warn(
                    "Flash Attention 2 requested but not available, using eager attention"
                )

        # Load the model
        model = AutoModel.from_pretrained(self.config.model_name, **model_kwargs)

        return model

    def _optimize_model(self) -> None:
        """Apply post-loading optimizations."""
        if self._model is None:
            return

        # Set to evaluation mode for inference
        self._model.eval()

        # Disable gradients for inference (can be re-enabled for training)
        for param in self._model.parameters():
            param.requires_grad_(False)

        # Enable gradient checkpointing if available (saves memory)
        if hasattr(self._model, "gradient_checkpointing_enable"):
            try:
                self._model.gradient_checkpointing_enable()
                logger.info("Enabled gradient checkpointing")
            except Exception as e:
                logger.warning(f"Could not enable gradient checkpointing: {e}")

    def _count_parameters(self) -> int:
        """Count total model parameters."""
        if self._model is None:
            return 0
        return sum(p.numel() for p in self._model.parameters())

    def get_model_info(self) -> Dict[str, Any]:
        """
        Get comprehensive model information.

        Returns:
            Dictionary containing model metadata
        """
        if self._model is None or self._model_config is None:
            raise RuntimeError(
                "Model not loaded. Call load_model_and_tokenizer() first."
            )

        info = {
            "model_name": self.config.model_name,
            "architecture": getattr(self._model_config, "architectures", ["Unknown"]),
            "model_type": getattr(self._model_config, "model_type", "Unknown"),
            "hidden_size": getattr(self._model_config, "hidden_size", None),
            "num_layers": getattr(self._model_config, "num_hidden_layers", None),
            "num_attention_heads": getattr(
                self._model_config, "num_attention_heads", None
            ),
            "vocab_size": getattr(self._model_config, "vocab_size", None),
            "max_position_embeddings": getattr(
                self._model_config, "max_position_embeddings", None
            ),
            "total_parameters": self._count_parameters(),
            "device": next(self._model.parameters()).device if self._model else None,
            "dtype": next(self._model.parameters()).dtype if self._model else None,
        }

        return info

    def get_hook_names(self) -> Dict[str, str]:
        """
        Get common hook names for the loaded model.

        Returns:
            Dictionary mapping hook types to hook name patterns
        """
        if self._model is None:
            raise RuntimeError(
                "Model not loaded. Call load_model_and_tokenizer() first."
            )

        # Detect actual model structure to determine correct hook patterns
        model_type = getattr(self._model_config, "model_type", "").lower()
        model_class_name = self._model.__class__.__name__

        # Check if model has nested 'model' attribute (like LlamaForCausalLM)
        # or direct access to layers (like LlamaModel)
        has_nested_model = hasattr(self._model, "model") and hasattr(
            self._model.model, "layers"
        )
        has_direct_layers = hasattr(self._model, "layers")

        # Determine the correct prefix for layer access
        if has_nested_model:
            layer_prefix = "model.layers"
        elif has_direct_layers:
            layer_prefix = "layers"
        else:
            # Fallback - try to detect from model structure
            layer_prefix = "layers"  # Most common case

        logger.debug(
            f"Model class: {model_class_name}, has_nested_model: {has_nested_model}, has_direct_layers: {has_direct_layers}"
        )
        logger.debug(f"Using layer prefix: {layer_prefix}")

        # Hook patterns for different architectures
        hook_patterns = {
            "llama": {
                "residual_pre": f"{layer_prefix}.{{layer}}.input_layernorm",
                "residual_post": f"{layer_prefix}.{{layer}}",
                "attn_out": f"{layer_prefix}.{{layer}}.self_attn",
                "mlp_out": f"{layer_prefix}.{{layer}}.mlp",
            },
            "gpt2": {
                "residual_pre": "transformer.h.{layer}.ln_1",
                "residual_post": "transformer.h.{layer}",
                "attn_out": "transformer.h.{layer}.attn",
                "mlp_out": "transformer.h.{layer}.mlp",
            },
            "bert": {
                "residual_pre": "encoder.layer.{layer}.attention.output.LayerNorm",
                "residual_post": "encoder.layer.{layer}",
                "attn_out": "encoder.layer.{layer}.attention.output",
                "mlp_out": "encoder.layer.{layer}.output",
            },
            "gemma": {
                "residual_pre": f"{layer_prefix}.{{layer}}.input_layernorm",
                "residual_post": f"{layer_prefix}.{{layer}}",
                "attn_out": f"{layer_prefix}.{{layer}}.self_attn",
                "mlp_out": f"{layer_prefix}.{{layer}}.mlp",
            },
        }

        # Try to match model type to known patterns
        for pattern_type, patterns in hook_patterns.items():
            if pattern_type in model_type:
                return patterns

        # Default patterns (similar to GPT-2)
        logger.warning(f"Unknown model type {model_type}, using default hook patterns")
        return hook_patterns["gpt2"]

    @property
    def model(self) -> Optional[PreTrainedModel]:
        """Get the loaded model."""
        return self._model

    @property
    def tokenizer(self) -> Optional[PreTrainedTokenizer]:
        """Get the loaded tokenizer."""
        return self._tokenizer

    @property
    def model_config(self) -> Optional[AutoConfig]:
        """Get the model configuration."""
        return self._model_config
