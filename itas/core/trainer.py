"""
SAE Trainer for training Sparse Auto-Encoders.

This module provides a comprehensive trainer for SAE models with support
for various training strategies, evaluation, and monitoring.
"""

import logging
from typing import Optional, Dict, Any, Callable, List
import torch
import torch.nn as nn
from torch.optim import Adam, AdamW
from torch.optim.lr_scheduler import ConstantLR, LinearLR, CosineAnnealingLR, OneCycleLR
from tqdm.auto import tqdm
import wandb
from pathlib import Path
import json
import time

from .config import SAEConfig
from .sae import TrainingSAE, SAEOutput
from .model_loader import UniversalModelLoader
from .dataset_manager import DatasetManager
from .activations_store import ActivationsStore

logger = logging.getLogger(__name__)


class SAETrainer:
    """
    Comprehensive trainer for Sparse Auto-Encoders.

    Supports multiple training strategies, automatic evaluation,
    checkpointing, and monitoring with Weights & Biases.
    """

    def __init__(
        self,
        config: SAEConfig,
        model_loader: Optional[UniversalModelLoader] = None,
        dataset_manager: Optional[DatasetManager] = None,
    ):
        """
        Initialize SAE trainer.

        Args:
            config: SAE configuration
            model_loader: Pre-initialized model loader (optional)
            dataset_manager: Pre-initialized dataset manager (optional)
        """
        self.config = config

        # Initialize components
        if model_loader is None:
            self.model_loader = UniversalModelLoader(config.model)
            self.model, self.tokenizer = self.model_loader.load_model_and_tokenizer()
        else:
            self.model_loader = model_loader
            self.model = model_loader.model
            self.tokenizer = model_loader.tokenizer

        if dataset_manager is None:
            self.dataset_manager = DatasetManager(config.dataset, self.tokenizer)
            self.dataset_manager.load_dataset()
            self.dataset_manager.preprocess_dataset()
        else:
            self.dataset_manager = dataset_manager

        # Initialize SAE
        self.sae = None
        self.optimizer = None
        self.scheduler = None

        # Training state
        self.current_step = 0
        self.current_token = 0
        self.best_loss = float("inf")

        # Monitoring
        self.training_metrics = []
        self.eval_metrics = []

        # Setup logging
        if config.training.use_wandb:
            self._setup_wandb()

    def _setup_wandb(self) -> None:
        """Setup Weights & Biases logging."""
        try:
            wandb.init(
                project=self.config.training.wandb_project or "sae-training",
                entity=self.config.training.wandb_entity,
                config=self.config.to_dict(),
                name=f"sae-{self.config.model.model_name.split('/')[-1]}-{self.config.hook_layer}",
            )
            logger.info("Initialized Weights & Biases logging")
        except Exception as e:
            logger.warning(f"Failed to initialize W&B: {e}")
            self.config.training.use_wandb = False

    def _initialize_sae(self) -> None:
        """Initialize SAE model."""
        # Get model info to determine d_in
        model_info = self.model_loader.get_model_info()

        if self.config.d_in is None:
            # Auto-detect input dimension from model
            self.config.d_in = model_info.get("hidden_size")
            if self.config.d_in is None:
                raise ValueError(
                    "Could not auto-detect d_in. Please specify in config."
                )

        # Compute d_sae if not specified
        if self.config.d_sae is None:
            self.config.d_sae = self.config.d_in * self.config.expansion_factor

        # Create SAE
        self.sae = TrainingSAE(
            d_in=self.config.d_in,
            d_sae=self.config.d_sae,
            architecture=self.config.architecture,
            activation_fn=self.config.activation_fn,
            normalize_decoder=self.config.normalize_decoder,
            device=self.config.device,
            dtype=getattr(torch, self.config.dtype),
        )

        logger.info(
            f"Initialized SAE: d_in={self.config.d_in}, d_sae={self.config.d_sae}"
        )

    def _initialize_optimizer(self) -> None:
        """Initialize optimizer and scheduler."""
        # Create optimizer
        self.optimizer = Adam(
            self.sae.parameters(),
            lr=self.config.training.learning_rate,
            betas=(self.config.training.adam_beta1, self.config.training.adam_beta2),
            weight_decay=self.config.training.weight_decay,
        )

        # Create scheduler
        total_steps = (
            self.config.training.total_training_tokens
            // self.config.training.batch_size
        )

        if self.config.training.lr_scheduler == "constant":
            self.scheduler = ConstantLR(self.optimizer, factor=1.0)
        elif self.config.training.lr_scheduler == "linear":
            self.scheduler = LinearLR(
                self.optimizer,
                start_factor=1.0,
                end_factor=0.1,
                total_iters=total_steps,
            )
        elif self.config.training.lr_scheduler == "cosine":
            self.scheduler = CosineAnnealingLR(
                self.optimizer,
                T_max=total_steps,
                eta_min=self.config.training.learning_rate * 0.1,
            )
        elif self.config.training.lr_scheduler == "onecycle":
            self.scheduler = OneCycleLR(
                self.optimizer,
                max_lr=self.config.training.learning_rate,
                total_steps=total_steps,
                pct_start=0.1,
            )
        else:
            raise ValueError(f"Unknown scheduler: {self.config.training.lr_scheduler}")

        logger.info(
            f"Initialized optimizer and {self.config.training.lr_scheduler} scheduler"
        )

    def train(self) -> TrainingSAE:
        """
        Train the SAE model.

        Returns:
            Trained SAE model
        """
        logger.info("Starting SAE training")

        # Initialize components
        self._initialize_sae()
        self._initialize_optimizer()

        # Setup activations store
        activations_store = ActivationsStore(
            self.model,
            self.tokenizer,
            self.config,
            self.dataset_manager,
        )

        # Training loop
        try:
            with activations_store:
                self._training_loop(activations_store)
        except KeyboardInterrupt:
            logger.info("Training interrupted by user")
        except Exception as e:
            logger.error(f"Training failed: {e}")
            raise
        finally:
            if self.config.training.use_wandb:
                wandb.finish()

        logger.info("Training completed")
        return self.sae

    def _training_loop(self, activations_store: ActivationsStore) -> None:
        """Main training loop."""
        # Calculate training parameters
        total_tokens = self.config.training.total_training_tokens
        batch_size = self.config.training.batch_size
        total_steps = total_tokens // batch_size

        # Progress bar
        pbar = tqdm(
            total=total_tokens,
            desc="Training SAE",
            unit="tokens",
            unit_scale=True,
        )

        # Start streaming activations with a smaller, more compatible batch size
        # Use a power of 2 that's likely to be compatible with attention mechanisms
        streaming_batch_size = min(
            16, batch_size // 256
        )  # Much smaller than training batch
        if streaming_batch_size < 1:
            streaming_batch_size = 1

        logger.info(
            f"Starting activation streaming with batch_size={streaming_batch_size}"
        )
        activations_store.start_streaming(batch_size=streaming_batch_size)

        # Track consecutive failed attempts to get activations
        consecutive_failures = 0
        max_consecutive_failures = 100  # Prevent infinite loop

        try:
            while self.current_token < total_tokens:
                # Get batch of activations
                activations = activations_store.get_next_batch(timeout=5.0)
                if activations is None:
                    consecutive_failures += 1
                    logger.warning(
                        f"No activations available, skipping step (failure {consecutive_failures}/{max_consecutive_failures})"
                    )

                    # Check if we've failed too many times consecutively
                    if consecutive_failures >= max_consecutive_failures:
                        logger.error(
                            f"Failed to get activations for {max_consecutive_failures} consecutive attempts. "
                            f"This likely indicates a problem with activation streaming. Stopping training."
                        )
                        break

                    # Try to restart streaming if it has died
                    if consecutive_failures % 10 == 0:  # Check every 10 failures
                        restarted = activations_store.restart_streaming_if_needed(
                            batch_size=streaming_batch_size
                        )
                        if restarted:
                            logger.info("Attempted to restart activation streaming")

                    # Add a small delay to avoid busy waiting
                    import time

                    time.sleep(0.1)
                    continue

                # Reset failure counter on successful activation retrieval
                consecutive_failures = 0

                # Move to device and reshape
                activations = activations.to(self.config.device)
                if len(activations.shape) > 2:
                    # Flatten sequence dimension
                    activations = activations.view(-1, activations.shape[-1])

                # Limit batch size
                if activations.shape[0] > batch_size:
                    activations = activations[:batch_size]

                # Training step
                loss_dict = self._training_step(activations)

                # Update counters
                self.current_step += 1
                self.current_token += activations.shape[0]

                # Update progress bar
                pbar.update(activations.shape[0])
                pbar.set_postfix(loss_dict)

                # Logging
                if self.current_step % self.config.training.log_every_n_steps == 0:
                    self._log_metrics(loss_dict)

                # Evaluation
                if (
                    self.current_token % self.config.training.eval_every_n_tokens == 0
                    and self.current_token > 0
                ):
                    self._evaluate(activations_store)

                # Checkpointing
                if (
                    self.current_token % self.config.training.checkpoint_every_n_tokens
                    == 0
                    and self.current_token > 0
                ):
                    self._save_checkpoint()

        finally:
            pbar.close()
            activations_store.stop_streaming()

            # Log final training state
            if consecutive_failures >= max_consecutive_failures:
                logger.warning(
                    f"Training stopped due to activation streaming failure. "
                    f"Processed {self.current_token}/{total_tokens} tokens "
                    f"({self.current_token/total_tokens*100:.1f}%)"
                )
            else:
                logger.info(
                    f"Training completed successfully. "
                    f"Processed {self.current_token}/{total_tokens} tokens"
                )

    def _training_step(self, activations: torch.Tensor) -> Dict[str, float]:
        """
        Perform a single training step.

        Args:
            activations: Batch of activations

        Returns:
            Dictionary of loss values
        """
        self.sae.train()

        # Ensure activations are in the correct dtype (float32) for training
        if activations.dtype != torch.float32:
            activations = activations.float()

        # Compute L1 coefficient (with warmup)
        l1_coeff = self._get_l1_coefficient()

        # Forward pass
        output = self.sae.training_forward(
            activations,
            l1_coefficient=l1_coeff,
        )

        # Total loss
        total_loss = output.mse_loss + output.l1_loss
        if output.aux_loss is not None:
            total_loss = total_loss + output.aux_loss

        # Backward pass
        self.optimizer.zero_grad()
        total_loss.backward()

        # Gradient clipping
        torch.nn.utils.clip_grad_norm_(self.sae.parameters(), max_norm=1.0)

        # Optimizer step
        self.optimizer.step()
        self.scheduler.step()

        # Normalize decoder weights if needed
        if hasattr(self.sae, "normalize_decoder") and callable(
            self.sae.normalize_decoder
        ):
            self.sae.normalize_decoder()

        # Return metrics
        metrics = {
            "total_loss": total_loss.item(),
            "mse_loss": output.mse_loss.item(),
            "l1_loss": output.l1_loss.item(),
            "fvu": output.fvu.item(),
            "sparsity": output.sparsity.item(),
            "l1_coeff": l1_coeff,
            "lr": self.scheduler.get_last_lr()[0],
        }

        if output.aux_loss is not None:
            metrics["aux_loss"] = output.aux_loss.item()

        return metrics

    def _get_l1_coefficient(self) -> float:
        """Get L1 coefficient with warmup."""
        if self.current_step < self.config.training.l1_warm_up_steps:
            # Linear warmup
            warmup_factor = self.current_step / self.config.training.l1_warm_up_steps
            return self.config.training.l1_coefficient * warmup_factor
        else:
            return self.config.training.l1_coefficient

    def _evaluate(self, activations_store: ActivationsStore) -> Dict[str, float]:
        """
        Evaluate the SAE model.

        Args:
            activations_store: Activations store for evaluation data

        Returns:
            Dictionary of evaluation metrics
        """
        logger.info("Running evaluation")

        self.sae.eval()
        eval_metrics = []

        with torch.no_grad():
            # Collect evaluation activations
            eval_activations = activations_store.collect_activations(
                num_batches=10,
                batch_size=32,
            )

            # Move to device and ensure correct dtype
            eval_activations = eval_activations.to(self.config.device)
            if eval_activations.dtype != torch.float32:
                eval_activations = eval_activations.float()

            # Evaluate in chunks to avoid memory issues
            chunk_size = 1000
            for i in range(0, eval_activations.shape[0], chunk_size):
                chunk = eval_activations[i : i + chunk_size]
                output = self.sae(chunk)

                metrics = {
                    "eval_mse_loss": output.mse_loss.item(),
                    "eval_l1_loss": output.l1_loss.item(),
                    "eval_fvu": output.fvu.item(),
                    "eval_sparsity": output.sparsity.item(),
                }
                eval_metrics.append(metrics)

        # Average metrics
        avg_metrics = {}
        for key in eval_metrics[0].keys():
            avg_metrics[key] = sum(m[key] for m in eval_metrics) / len(eval_metrics)

        # Log evaluation metrics
        self._log_metrics(avg_metrics)

        # Update best loss
        if avg_metrics["eval_mse_loss"] < self.best_loss:
            self.best_loss = avg_metrics["eval_mse_loss"]
            logger.info(f"New best evaluation loss: {self.best_loss:.6f}")

        return avg_metrics

    def _log_metrics(self, metrics: Dict[str, float]) -> None:
        """Log metrics to console and W&B."""
        # Add step information
        metrics.update(
            {
                "step": self.current_step,
                "tokens": self.current_token,
            }
        )

        # Log to W&B
        if self.config.training.use_wandb:
            wandb.log(metrics)

        # Store metrics
        self.training_metrics.append(metrics)

    def _save_checkpoint(self) -> None:
        """Save training checkpoint."""
        if self.config.training.save_checkpoint_dir is None:
            return

        checkpoint_dir = Path(self.config.training.save_checkpoint_dir)
        checkpoint_dir.mkdir(parents=True, exist_ok=True)

        checkpoint_path = checkpoint_dir / f"checkpoint_step_{self.current_step}.pt"

        checkpoint = {
            "step": self.current_step,
            "tokens": self.current_token,
            "sae_state_dict": self.sae.state_dict(),
            "optimizer_state_dict": self.optimizer.state_dict(),
            "scheduler_state_dict": self.scheduler.state_dict(),
            "config": self.config.to_dict(),
            "best_loss": self.best_loss,
        }

        torch.save(checkpoint, checkpoint_path)
        logger.info(f"Saved checkpoint to {checkpoint_path}")

    def load_checkpoint(self, checkpoint_path: str) -> None:
        """
        Load training checkpoint.

        Args:
            checkpoint_path: Path to checkpoint file
        """
        checkpoint = torch.load(checkpoint_path, map_location=self.config.device)

        # Initialize SAE if not done
        if self.sae is None:
            self._initialize_sae()
            self._initialize_optimizer()

        # Load states
        self.sae.load_state_dict(checkpoint["sae_state_dict"])
        self.optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
        self.scheduler.load_state_dict(checkpoint["scheduler_state_dict"])

        # Load training state
        self.current_step = checkpoint["step"]
        self.current_token = checkpoint["tokens"]
        self.best_loss = checkpoint["best_loss"]

        logger.info(f"Loaded checkpoint from {checkpoint_path}")
        logger.info(
            f"Resuming from step {self.current_step}, token {self.current_token}"
        )

    def save_model(self, save_path: str) -> None:
        """
        Save trained SAE model.

        Args:
            save_path: Path to save the model
        """
        if self.sae is None:
            raise ValueError("No trained SAE to save")

        save_dict = {
            "sae_state_dict": self.sae.state_dict(),
            "config": self.config.to_dict(),
            "training_metrics": self.training_metrics,
        }

        torch.save(save_dict, save_path)
        logger.info(f"Saved SAE model to {save_path}")

    def get_training_metrics(self) -> Dict[str, Any]:
        """
        Get training metrics summary.

        Returns:
            Dictionary containing training metrics summary
        """
        if not self.training_metrics:
            return {}

        # Get the last recorded metrics
        last_metrics = self.training_metrics[-1] if self.training_metrics else {}

        return {
            "total_steps": self.current_step,
            "total_tokens": self.current_token,
            "final_loss": last_metrics.get("total_loss"),
            "final_mse_loss": last_metrics.get("mse_loss"),
            "final_l1_loss": last_metrics.get("l1_loss"),
            "final_sparsity": last_metrics.get("sparsity"),
            "final_fvu": last_metrics.get("fvu"),
            "best_eval_loss": (
                self.best_loss if self.best_loss != float("inf") else None
            ),
            "num_metrics_recorded": len(self.training_metrics),
        }
