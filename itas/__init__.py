"""
ITAS: Instruction-Truth Activation Steering

A comprehensive Python library for training and using Sparse Auto-Encoders (SAEs)
with any HuggingFace transformer model for representation engineering tasks.

Key Features:
- Universal HuggingFace model support
- Flexible dataset handling
- Multiple SAE architectures (standard, gated, jumprelu)
- Comprehensive evaluation tools
- Easy-to-use API

Example:
    >>> import itas
    >>> model_config = itas.ModelConfig(model_name="microsoft/DialoGPT-medium")
    >>> sae_config = itas.SAEConfig(model_config=model_config)
    >>> trainer = itas.SAETrainer(sae_config)
    >>> sae = trainer.train()
"""

__version__ = "0.1.0"
__author__ = "Jun Kim"
__email__ = "<EMAIL>"

# Core SAE components
from .core import (
    SAEConfig,
    ModelConfig,
    DatasetConfig,
    TrainingConfig,
    SAETrainer,
    SAE,
    UniversalModelLoader,
    DatasetManager,
)

# Analysis and evaluation tools
from .analysis import (
    ActivationA<PERSON>yzer,
    FunctionExtractor,
    RepresentationEngineer,
    SAEEvaluator,
    SAEVisualizer,
)

# Utilities
from .utils import (
    load_model_and_tokenizer,
    setup_logging,
    validate_config,
    create_sae_config,
)

__all__ = [
    # Core components
    "SAEConfig",
    "ModelConfig",
    "DatasetConfig",
    "TrainingConfig",
    "SAETrainer",
    "SAE",
    "UniversalModelLoader",
    "DatasetManager",
    # Analysis tools
    "ActivationAnalyzer",
    "FunctionExtractor",
    "RepresentationEngineer",
    "SAEEvaluator",
    "SAEVisualizer",
    # Utilities
    "load_model_and_tokenizer",
    "setup_logging",
    "validate_config",
    "create_sae_config",
]
