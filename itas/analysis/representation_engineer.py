"""
Representation Engineer for SAE-based model interventions.

This module provides tools for engineering model representations using
SAE features to achieve targeted behavioral modifications.
"""

import logging
from typing import Dict, Any, List, Optional, Callable, Tuple
import torch
import torch.nn as nn
from torch import Tensor
import numpy as np
from dataclasses import dataclass
from transformers import PreTrainedModel, PreTrainedTokenizer

from ..core.sae import SAE
from .function_extractor import FunctionExtractor, FunctionExtractionResult

logger = logging.getLogger(__name__)


@dataclass
class InterventionResult:
    """Result from a representation intervention."""
    
    original_output: Any
    """Original model output before intervention"""
    
    modified_output: Any
    """Modified model output after intervention"""
    
    intervention_strength: float
    """Strength of the applied intervention"""
    
    affected_features: List[int]
    """List of SAE features affected by intervention"""
    
    metadata: Dict[str, Any]
    """Additional metadata about the intervention"""


class RepresentationEngineer:
    """
    Engineer model representations using SAE features.
    
    Provides tools for targeted model interventions through SAE feature
    manipulation to achieve specific behavioral modifications.
    """
    
    def __init__(
        self,
        model: PreTrainedModel,
        tokenizer: PreTrainedTokenizer,
        sae: SAE,
        hook_layer: int,
        hook_name: str,
    ):
        """
        Initialize representation engineer.
        
        Args:
            model: Target model for interventions
            tokenizer: Tokenizer for the model
            sae: Trained SAE for feature extraction
            hook_layer: Layer to apply interventions
            hook_name: Name of the hook point
        """
        self.model = model
        self.tokenizer = tokenizer
        self.sae = sae
        self.hook_layer = hook_layer
        self.hook_name = hook_name
        
        # Intervention state
        self.active_hooks = []
        self.intervention_functions = {}
        
    def create_steering_vector(
        self,
        positive_examples: List[str],
        negative_examples: List[str],
        method: str = "difference",
    ) -> Tensor:
        """
        Create a steering vector from positive and negative examples.
        
        Args:
            positive_examples: Examples representing desired behavior
            negative_examples: Examples representing undesired behavior
            method: Method for creating steering vector
            
        Returns:
            Steering vector in activation space
        """
        logger.info(f"Creating steering vector from {len(positive_examples)} positive and {len(negative_examples)} negative examples")
        
        # Get activations for examples
        pos_activations = self._get_activations_for_texts(positive_examples)
        neg_activations = self._get_activations_for_texts(negative_examples)
        
        if method == "difference":
            # Simple difference method
            pos_mean = pos_activations.mean(dim=0)
            neg_mean = neg_activations.mean(dim=0)
            steering_vector = pos_mean - neg_mean
            
        elif method == "pca":
            # PCA-based method
            all_activations = torch.cat([pos_activations, neg_activations], dim=0)
            labels = torch.cat([
                torch.ones(len(pos_activations)),
                torch.zeros(len(neg_activations))
            ])
            
            # Compute PCA direction
            centered = all_activations - all_activations.mean(dim=0)
            U, S, V = torch.pca_lowrank(centered, q=1)
            steering_vector = V[:, 0]
            
            # Ensure direction points toward positive examples
            pos_proj = (pos_activations @ steering_vector).mean()
            neg_proj = (neg_activations @ steering_vector).mean()
            if pos_proj < neg_proj:
                steering_vector = -steering_vector
                
        else:
            raise ValueError(f"Unknown steering method: {method}")
            
        logger.info(f"Created steering vector with norm: {steering_vector.norm().item():.4f}")
        return steering_vector
        
    def _get_activations_for_texts(self, texts: List[str]) -> Tensor:
        """Get model activations for a list of texts."""
        all_activations = []
        
        with torch.no_grad():
            for text in texts:
                # Tokenize
                inputs = self.tokenizer(text, return_tensors="pt", truncation=True)
                input_ids = inputs["input_ids"].to(self.model.device)
                
                # Get activations using hook
                activations = self._extract_activations(input_ids)
                
                # Use last token activation (or mean)
                if len(activations.shape) == 3:  # [batch, seq, hidden]
                    activation = activations[0, -1, :]  # Last token
                else:
                    activation = activations[0]  # Already 2D
                    
                all_activations.append(activation)
                
        return torch.stack(all_activations)
        
    def _extract_activations(self, input_ids: Tensor) -> Tensor:
        """Extract activations from model at specified layer."""
        activations = []
        
        def hook_fn(module, input, output):
            if isinstance(output, tuple):
                activations.append(output[0].detach())
            else:
                activations.append(output.detach())
                
        # Register hook
        target_module = self.model
        for part in self.hook_name.split('.'):
            target_module = getattr(target_module, part)
            
        hook_handle = target_module.register_forward_hook(hook_fn)
        
        try:
            # Forward pass
            with torch.no_grad():
                _ = self.model(input_ids)
                
            return activations[0] if activations else None
        finally:
            hook_handle.remove()
            
    def apply_steering_intervention(
        self,
        steering_vector: Tensor,
        strength: float = 1.0,
        method: str = "add",
    ) -> Callable:
        """
        Create intervention function for steering.
        
        Args:
            steering_vector: Vector to steer toward
            strength: Strength of intervention
            method: Method for applying intervention
            
        Returns:
            Intervention function
        """
        def intervention_fn(module, input, output):
            if isinstance(output, tuple):
                hidden_states = output[0]
                other_outputs = output[1:]
            else:
                hidden_states = output
                other_outputs = ()
                
            # Apply intervention
            if method == "add":
                # Add steering vector
                modified = hidden_states + strength * steering_vector.unsqueeze(0).unsqueeze(0)
            elif method == "project":
                # Project onto steering direction
                projection = (hidden_states @ steering_vector) / steering_vector.norm()
                modified = hidden_states + strength * projection.unsqueeze(-1) * steering_vector
            else:
                raise ValueError(f"Unknown intervention method: {method}")
                
            if other_outputs:
                return (modified,) + other_outputs
            else:
                return modified
                
        return intervention_fn
        
    def apply_feature_intervention(
        self,
        feature_indices: List[int],
        intervention_values: List[float],
        method: str = "set",
    ) -> Callable:
        """
        Create intervention function for specific SAE features.
        
        Args:
            feature_indices: Indices of features to intervene on
            intervention_values: Values to set/add for each feature
            method: Method for intervention (set, add, multiply)
            
        Returns:
            Intervention function
        """
        def intervention_fn(module, input, output):
            if isinstance(output, tuple):
                hidden_states = output[0]
                other_outputs = output[1:]
            else:
                hidden_states = output
                other_outputs = ()
                
            # Encode through SAE
            sae_features = self.sae.encode(hidden_states)
            
            # Apply feature interventions
            for feat_idx, value in zip(feature_indices, intervention_values):
                if method == "set":
                    sae_features[:, :, feat_idx] = value
                elif method == "add":
                    sae_features[:, :, feat_idx] += value
                elif method == "multiply":
                    sae_features[:, :, feat_idx] *= value
                else:
                    raise ValueError(f"Unknown intervention method: {method}")
                    
            # Decode back to activation space
            modified = self.sae.decode(sae_features)
            
            if other_outputs:
                return (modified,) + other_outputs
            else:
                return modified
                
        return intervention_fn
        
    def register_intervention(
        self,
        intervention_fn: Callable,
        intervention_name: str,
    ) -> None:
        """
        Register an intervention function.
        
        Args:
            intervention_fn: Function to apply intervention
            intervention_name: Name for the intervention
        """
        # Get target module
        target_module = self.model
        for part in self.hook_name.split('.'):
            target_module = getattr(target_module, part)
            
        # Register hook
        hook_handle = target_module.register_forward_hook(intervention_fn)
        
        # Store for later removal
        self.active_hooks.append(hook_handle)
        self.intervention_functions[intervention_name] = intervention_fn
        
        logger.info(f"Registered intervention: {intervention_name}")
        
    def remove_intervention(self, intervention_name: str) -> None:
        """
        Remove a specific intervention.
        
        Args:
            intervention_name: Name of intervention to remove
        """
        if intervention_name in self.intervention_functions:
            del self.intervention_functions[intervention_name]
            logger.info(f"Removed intervention: {intervention_name}")
        else:
            logger.warning(f"Intervention not found: {intervention_name}")
            
    def remove_all_interventions(self) -> None:
        """Remove all active interventions."""
        for hook_handle in self.active_hooks:
            hook_handle.remove()
            
        self.active_hooks.clear()
        self.intervention_functions.clear()
        
        logger.info("Removed all interventions")
        
    def test_intervention(
        self,
        test_texts: List[str],
        intervention_fn: Callable,
        generation_kwargs: Optional[Dict[str, Any]] = None,
    ) -> InterventionResult:
        """
        Test an intervention on given texts.
        
        Args:
            test_texts: Texts to test intervention on
            intervention_fn: Intervention function to apply
            generation_kwargs: Arguments for text generation
            
        Returns:
            Intervention test results
        """
        if generation_kwargs is None:
            generation_kwargs = {"max_new_tokens": 20, "do_sample": False}
            
        results = []
        
        for text in test_texts:
            # Get original output
            inputs = self.tokenizer(text, return_tensors="pt")
            input_ids = inputs["input_ids"].to(self.model.device)
            
            with torch.no_grad():
                original_output = self.model.generate(input_ids, **generation_kwargs)
                
            # Apply intervention and get modified output
            self.register_intervention(intervention_fn, "test_intervention")
            
            try:
                with torch.no_grad():
                    modified_output = self.model.generate(input_ids, **generation_kwargs)
            finally:
                self.remove_intervention("test_intervention")
                
            # Decode outputs
            original_text = self.tokenizer.decode(original_output[0], skip_special_tokens=True)
            modified_text = self.tokenizer.decode(modified_output[0], skip_special_tokens=True)
            
            results.append({
                'input': text,
                'original': original_text,
                'modified': modified_text,
            })
            
        return InterventionResult(
            original_output=results,
            modified_output=results,
            intervention_strength=1.0,  # Default
            affected_features=[],  # Would need to track this
            metadata={'test_texts': test_texts, 'generation_kwargs': generation_kwargs}
        )
        
    def extract_behavioral_function(
        self,
        behavior_examples: Dict[str, List[str]],
        extraction_method: str = "contrastive",
    ) -> FunctionExtractionResult:
        """
        Extract a behavioral function from examples.
        
        Args:
            behavior_examples: Dictionary with 'positive' and 'negative' example lists
            extraction_method: Method for extracting the function
            
        Returns:
            Function extraction result
        """
        logger.info("Extracting behavioral function from examples")
        
        # Get activations for positive and negative examples
        pos_activations = self._get_activations_for_texts(behavior_examples['positive'])
        neg_activations = self._get_activations_for_texts(behavior_examples['negative'])
        
        # Create function extractor
        function_extractor = FunctionExtractor(self.sae)
        
        if extraction_method == "contrastive":
            # Use positive examples as target, negative as context
            result = function_extractor.extract_function(
                target_activations=pos_activations,
                context_activations=neg_activations,
                learning_rate=1e-3,
                num_iterations=500,
            )
        else:
            raise ValueError(f"Unknown extraction method: {extraction_method}")
            
        logger.info(f"Extracted function with {len(result.active_features)} active features")
        return result
        
    def __enter__(self):
        """Context manager entry."""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - clean up interventions."""
        self.remove_all_interventions()
