# Include the README and other important files
include README.md
include LICENSE
include CONTRIBUTING.md
include REBRANDING_SUMMARY.md
include REFACTORED_LIBRARY_GUIDE.md

# Include the tutorial notebook
include tutorial.ipynb

# Include demo script
include demo.py

# Include package data
recursive-include itas *.py
recursive-include itas *.yaml
recursive-include itas *.json
recursive-include itas *.txt

# Include any data files needed by the package
recursive-include itas/sae_lens/toolkit *.json

# Exclude unnecessary files
exclude .gitignore
exclude .pre-commit-config.yaml
exclude pyproject.toml
exclude *.log
exclude *.tmp

# Exclude test files from distribution
recursive-exclude tests *
recursive-exclude * __pycache__
recursive-exclude * *.py[co]
recursive-exclude * *.orig

# Exclude development and build files
exclude .coverage
exclude .pytest_cache
exclude build
exclude dist
exclude *.egg-info

# Exclude IDE files
exclude .vscode
exclude .idea
exclude *.swp
exclude *.swo

# Exclude OS files
exclude .DS_Store
exclude Thumbs.db

# Exclude large model files and data
exclude *.pt
exclude *.pth
exclude *.bin
exclude *.safetensors
exclude checkpoints
exclude models
exclude data
exclude datasets
exclude logs
exclude outputs
exclude results
exclude wandb
